(function (window, document) {
    const ls9627Map = {
        entId: '', // 商户号
        channelId: '', // 渠道id
        language: 'en', // 语言
        qudao: '', // 语言
        clientId: '', // 客户id
        address: "", // 地址
        age: "", // 年龄
        comment: "", // 备注
        email: "", // 邮箱
        gender: "", // 性别
        name: "", // 名字
        username: "", // 用户名
        qq: "", // QQ
        tel: "", // 电话
        weixin: "", // 微信
        url: '', // 客服url地址
        iframeId: null, // 保存iframe相关ID
        showIframe: false, // 页面开关
        hiddenClose: '', // 是否显示关闭弹窗开关
        embedModel: '', // 嵌入模式，空(正常script嵌入) html(html模式)
    };

    let argMap = null;
    if (window._YUNKE.a && window._YUNKE.a[0] && window._YUNKE.a[0][0]) {
        argMap = window._YUNKE.a[0][0];
        console.log('客户传参: ', argMap);
        for (let k in argMap) {
            ls9627Map[k] = argMap[k];
        }
    }

    // 消息图片
    const msgIcon = `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjYiIGhlaWdodD0iMjYiIHZpZXdCb3g9IjAgMCAyNiAyNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgICAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEgNC4wODMzM0MxIDMuNDg1MDIgMS40ODUwMiAzIDIuMDgzMzMgM0gyMy43NUMyNC4zNDgzIDMgMjQuODMzMyAzLjQ4NTAyIDI0LjgzMzMgNC4wODMzM1YyMC4zMzMzQzI0LjgzMzMgMjAuOTMxNiAyNC4zNDgzIDIxLjQxNjcgMjMuNzUgMjEuNDE2N0gxNi4wNzM3TDEzLjY4MjcgMjMuODA3N0MxMy4yNTk2IDI0LjIzMDggMTIuNTczNyAyNC4yMzA4IDEyLjE1MDYgMjMuODA3N0w5Ljc1OTYgMjEuNDE2N0gyLjA4MzMzQzEuNDg1MDIgMjEuNDE2NyAxIDIwLjkzMTYgMSAyMC4zMzMzVjQuMDgzMzNaTTYuOTU4ODYgMTEuMTI1QzYuMzYwNTUgMTEuMTI1IDUuODc1NTMgMTEuNjEgNS44NzU1MyAxMi4yMDgzQzUuODc1NTMgMTIuODA2NiA2LjM2MDU1IDEzLjI5MTcgNi45NTg4NiAxMy4yOTE3SDguMDQxNkM4LjYzOTkxIDEzLjI5MTcgOS4xMjQ5MyAxMi44MDY2IDkuMTI0OTMgMTIuMjA4M0M5LjEyNDkzIDExLjYxIDguNjM5OTEgMTEuMTI1IDguMDQxNiAxMS4xMjVINi45NTg4NlpNMTEuMjkxNyAxMi4yMDgzQzExLjI5MTcgMTEuNjEgMTEuNzc2NyAxMS4xMjUgMTIuMzc1IDExLjEyNUgxMy40NTk3QzE0LjA1OCAxMS4xMjUgMTQuNTQzIDExLjYxIDE0LjU0MyAxMi4yMDgzQzE0LjU0MyAxMi44MDY2IDE0LjA1OCAxMy4yOTE3IDEzLjQ1OTcgMTMuMjkxN0gxMi4zNzVDMTEuNzc2NyAxMy4yOTE3IDExLjI5MTcgMTIuODA2NiAxMS4yOTE3IDEyLjIwODNaTTE3Ljc5MjIgMTEuMTI1QzE3LjE5MzkgMTEuMTI1IDE2LjcwODkgMTEuNjEgMTYuNzA4OSAxMi4yMDgzQzE2LjcwODkgMTIuODA2NiAxNy4xOTM5IDEzLjI5MTcgMTcuNzkyMiAxMy4yOTE3SDE4Ljg3NDlDMTkuNDczMiAxMy4yOTE3IDE5Ljk1ODMgMTIuODA2NiAxOS45NTgzIDEyLjIwODNDMTkuOTU4MyAxMS42MSAxOS40NzMyIDExLjEyNSAxOC44NzQ5IDExLjEyNUgxNy43OTIyWiIgZmlsbD0id2hpdGUiLz4KICAgICAgICA8L3N2Zz4=`;

    // 初始化样式
    let defaultStyle = {
        "pc_win_style": {
            "theme_color": "#2F4FED",
            "location": "1",
            "side_margin": 0,
            "bottom_margin": 0,
            "round_angle": 0,
            "window_width": 375,
            "window_height": 592,
            "head_height": 52
        },
        "h5_win_style": {
            "theme_color": "#2F4FED",
            "location": "2",
            "head_height": 52
        },
        "pc_btn_style": {
            "win_style": "1",
            "theme_color": "#2F4FED",
            "online_content": "我们在线来聊聊吧",
            "offline_content": "下班了，给我们留言吧",
            "location": "1",
            "side_margin": 0,
            "bottom_margin": 0,
            "message_preview": "off"
        },
        "h5_btn_style": {
            "win_style": "1",
            "theme_color": "#2F4FED",
            "online_content": "我们在线来聊聊吧",
            "offline_content": "下班了，给我们留言吧",
            "location": "1",
            "side_margin": 0,
            "bottom_margin": 0,
            "message_preview": "on"
        }
    }

    // 在线状态  
    let isOnline = false;

    // 请求样式
    function getInitStyle({ entId, channelId }, callback) {
        let ajax = new XMLHttpRequest();
        // 渠道是否可用
        let channelIsNormal = false;
        ajax.onreadystatechange = function () {
            if (this.readyState == 4) {
                try {
                    if (this.status == 200) {
                        const res = JSON.parse(this.responseText);
                        if (res && res.code === 0 && res.data && res.data.style) {
                            isOnline = res.data.status === 'Connected'; // NotConnected离线  Connected在线
                            defaultStyle = res.data.style;
                            channelIsNormal = true;
                            /*
                              channelId: res.data.unique_id
                              style: {
                                  h5_btn_style
                                  h5_win_style
                                  pc_btn_style
                                  pc_win_style
                              }
                            */
                        }

                    }
                } catch (e) {

                } finally {
                    callback(channelIsNormal);
                    console.log('样式请求完成', defaultStyle)
                }
            }
        };
        ajax.open(
            'post',
            `https://webuser.demoxapp.com/user/init-style`,
            true,
        );
        ajax.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
        ajax.send(JSON.stringify({ entId, channelId }));
    }

    // 初始化客服按钮
    function initYUNKE() {
        const {
            entId, channelId, language, clientId, address,
            age, comment, email, gender, name, qudao,
            qq, tel, weixin, username, hiddenClose,
            embedModel
        } = ls9627Map;
        if (entId && channelId) {
            console.log("商户号", entId);
            console.log("渠道id", channelId);
            getInitStyle({ entId, channelId }, (res) => {
                if (!res) { // 渠道不可用
                    return;
                }
                // 必选参数
                // 通过传入的id获取客服url地址
                let url = ls9627Map.url = `https://webuser.demoxapp.com/#/chat?entId=${entId}&channelId=${channelId}&language=${language}`;
                // let url = ls9627Map.url = `http://localhost:9009/#/chat?entId=${entId}&channelId=${channelId}&language=${language}`;

                // 可选参数
                if (clientId) url = ls9627Map.url = url + `&clientId=${clientId}`;
                if (address) url = ls9627Map.url = url + `&address=${address}`;
                if (qudao) url = ls9627Map.url = url + `&qudao=${qudao}`;
                if (age) url = ls9627Map.url = url + `&age=${age}`;
                if (comment) url = ls9627Map.url = url + `&comment=${comment}`;
                if (email) url = ls9627Map.url = url + `&email=${email}`;
                if (gender) url = ls9627Map.url = url + `&gender=${gender}`;
                if (name) url = ls9627Map.url = url + `&name=${name}`;
                if (username) url = ls9627Map.url = url + `&username=${username}`;
                if (qq) url = ls9627Map.url = url + `&qq=${qq}`;
                if (tel) url = ls9627Map.url = url + `&tel=${tel}`;
                if (weixin) url = ls9627Map.url = url + `&weixin=${weixin}`;
                if (hiddenClose) url = ls9627Map.url = url + `&hiddenClose=${hiddenClose}`;

                createStyle();
                listenWinReSize();
                if (url) {
                    if (embedModel === 'html') {
                        autoInitIframe(url)
                    } else {
                        createPcBtn(url);
                        createH5Btn(url);
                        createIframe(url);
                    }
                }
            });
        }
    }

    // 根据win_style获取iframe按钮的className
    function getBtnIframeClass(type) {
        let cls;
        if (type === 'pc') {
            const { win_style } = defaultStyle.pc_btn_style;
            let pcCls = {
                '1': 'kefu-btn-bottom-pc',
                '2': 'kefu-btn-round-pc',
                '3': 'kefu-btn-side-pc',
            }[win_style] || 'kefu-btn-bottom-pc';
            cls = `YUNKE-body-btn-9527 ${pcCls}`;
            // win_style 按钮样式 1底部 2圆形 3侧边
            if (win_style == 1) { // 横向按钮
                cls += ' horizontal';
            }
            if (win_style == 3) { // 竖直按钮
                cls += ' vertical';
            }
        } else {
            const { win_style } = defaultStyle.h5_btn_style;
            let h5Cls = {
                '1': 'kefu-btn-bottom-h5',
                '2': 'kefu-btn-round-h5',
                '3': 'kefu-btn-side-h5',
            }[win_style] || 'kefu-btn-bottom-h5';
            cls = `YUNKE-body-btn-9527 ${h5Cls}`;
            // win_style 按钮样式 1底部 2圆形 3侧边
            if (win_style == 1) { // 横向按钮
                cls += ' horizontal';
            }
            if (win_style == 3) { // 竖直按钮
                cls += ' vertical';
            }
        }

        return cls;
    }

    // 自动初始化页面
    function autoInitIframe(url) {
        createHtmlIframe(url);
        ls9627Map.showIframe = true;
        notifyIframe();
    }

    // 生成html类型客服界面iframe
    function createHtmlIframe(url) {
        let iframe = document.createElement('iframe');
        iframe.id = ls9627Map.iframeId = 'YUNKE' + Date.now();
        iframe.src = url;
        iframe.className = 'YUNKE-page-iframe-9527';
        iframe.className += ' fix-pc';
        document.body.appendChild(iframe);
        window.addEventListener('message', addCloseEvent);
    }

    // 客服按钮点击事件
    function clickKefuBtnEvent(url) {
        const id = ls9627Map.iframeId;
        if (!id) { // 页面初始化化
            createIframe(url);
            ls9627Map.showIframe = true;
            toggleIframe(id);
        } else { // 页面显示隐藏
            ls9627Map.showIframe = !ls9627Map.showIframe;
            toggleIframe(ls9627Map.iframeId);
        }
    }

    // 生产pc客服按钮
    function createPcBtn(url) {
        let btn = document.createElement('div');
        const { location, side_margin, bottom_margin, theme_color, win_style, online_content, offline_content } = defaultStyle.pc_btn_style;
        btn.className = getBtnIframeClass('pc');
        if (location == '1') { // 左侧
            btn.style.left = side_margin + 'px';
        }
        if (location == '2') { // 右侧
            btn.style.right = side_margin + 'px';
        }
        btn.style.bottom = bottom_margin + 'px'; // 底部
        btn.style.backgroundColor = theme_color || '#2F4FED'; // 主题色
        btn.addEventListener('click', () => clickKefuBtnEvent(url), false);

        const span1 = document.createElement('span');
        const img = document.createElement('img');
        img.src = msgIcon;
        span1.appendChild(img);
        btn.appendChild(span1);

        // const span2 = document.createElement('span');
        // span2.classList.add('yk-pc-btn-text');
        if (win_style != '2') { // 非圆形按钮
            let msg = isOnline ? online_content : offline_content;
            msg = msg || '下班了，给我们留言吧';
            // span2.innerText = msg; // 短文字
            let textBox =  createTextContent(msg, 'pc', msg && msg.length > 12 ? '' : 'short');
            btn.appendChild(textBox);
            // if (msg && msg.length > 12) { // 长文字
            //     btn.appendChild(textBox);
            // } else { // 短文字
            //     btn.appendChild(span2);
            // }
        }

        const previewBox = document.createElement('i');
        previewBox.className = location == '1' ? 'preview-box pc left' : 'preview-box pc';
        btn.appendChild(previewBox);
        document.body.appendChild(btn);
    }

    // 生产h5客服按钮
    function createH5Btn(url) {
        let btn = document.createElement('div');
        btn.className = getBtnIframeClass('h5');
        const { location, side_margin, bottom_margin, theme_color, win_style, online_content, offline_content } = defaultStyle.h5_btn_style;
        if (location == '1') { // 左侧
            btn.style.left = side_margin + 'px'
        }
        if (location == '2') { // 右侧
            btn.style.right = side_margin + 'px';
        }
        btn.style.bottom = bottom_margin + 'px'; // 底部
        btn.style.backgroundColor = theme_color || '#2F4FED'; // 主题色
        btn.addEventListener('click', () => clickKefuBtnEvent(url), false);

        const span1 = document.createElement('span');
        const img = document.createElement('img');
        img.src = msgIcon;
        span1.appendChild(img);
        btn.appendChild(span1);

        // const span2 = document.createElement('span');
        // span2.classList.add('yk-h5-btn-text');
        if (win_style != '2') { // 非圆形按钮
            let msg = isOnline ? online_content : offline_content;
            msg = msg || '下班了，给我们留言吧';
            // span2.innerText = msg; // 短文字
            let textBox =  createTextContent(msg, 'h5', msg && msg.length > 12 ? '' : 'short');
            btn.appendChild(textBox);
            // if (msg && msg.length > 12) { // 长文字
            //     btn.appendChild(textBox);
            // } else { // 短文字
            //     btn.appendChild(span2);
            // }
        }

        const previewBox = document.createElement('i');
        previewBox.className = location == '1' ? 'preview-box h5 left' : 'preview-box h5';
        btn.appendChild(previewBox);
        document.body.appendChild(btn);
    }

    // 生成客服界面iframe
    function createIframe(url) {
        let iframe = document.createElement('iframe');
        iframe.id = ls9627Map.iframeId = 'YUNKE' + Date.now();
        iframe.src = url;
        iframe.className = 'YUNKE-page-iframe-9527';
        iframe.style.display = ls9627Map.showIframe ? 'flex' : 'none';

        const width = document.documentElement.clientWidth || document.body.clientWidth;
        console.log("窗口宽度: ", width);
        if (width > 767) {
            const { location, side_margin, bottom_margin, round_angle, window_width, window_height } = defaultStyle.pc_win_style;

            // 左侧
            if (location == '1') iframe.style.left = (side_margin || 0) + 'px';
            // 右侧
            if (location == '2') iframe.style.right = (side_margin || 0) + 'px';
            // 中间
            if (location == '3') {
                iframe.style.left = '50%';
                iframe.style.transform = 'translateX(-50%)';
            }
            // 圆角
            iframe.style.borderRadius = (round_angle || 0) + 'px';
            // 聊天窗宽度
            iframe.style.width = (window_width || 375) + 'px';
            // 聊天窗高度
            iframe.style.height = (window_height || 592) + 'px';
            iframe.style.bottom = (bottom_margin || 0) + 'px';
        }
        document.body.appendChild(iframe);
        window.addEventListener('message', addCloseEvent);
    }

    // 删除iframe
    // function deleteIframe(id) {
    //     let iframe = document.getElementById(id);
    //     if (iframe) {
    //         // 把iframe指向空白页面，这样可以释放大部分内存。 
    //         iframe.src = 'about:blank';
    //         try {
    //             iframe.contentWindow.document.write('');
    //             iframe.contentWindow.document.clear();
    //         } catch (e) { }

    //         // 把iframe从页面移除 
    //         iframe.parentNode.removeChild(iframe);
    //     }
    // }

    // 通知iframe客服页面
    function notifyIframe() {
        let iframe = document.getElementById(ls9627Map.iframeId);
        if (iframe && ls9627Map.showIframe) {
            iframe.contentWindow.postMessage('getOnline', ls9627Map.url);
            clearPreviewBox();
        }
    }

    // 显示隐藏iframe
    function toggleIframe(id) {
        let iframe = document.getElementById(id);
        if (iframe) {
            iframe.style.display = ls9627Map.showIframe ? 'flex' : 'none';
            notifyIframe();
        }
    }

    // 监听客服页面事件
    function addCloseEvent(e) {
        console.log('监听客服页面事件e.data: ', e.data);
        if (e && e.data) {
            switch (e.data.msg) {
                case 'close': // 关闭窗口
                    ls9627Map.showIframe = false;
                    toggleIframe(ls9627Map.iframeId);
                    break;
                case 'waitCount': // 等待客服数量变更
                    //e.data.count
                    break;
                case 'online': // 客服在线状态
                    isOnline = e.data.data;
                    const pcBtn = document.querySelector('.marquee-content.pc');
                    const h5Btn = document.querySelector('.marquee-content.h5');
                    if (isOnline) { // 在线文案变化
                        console.log("isOnline:在线", isOnline);
                        if (pcBtn) pcBtn.innerText = defaultStyle.pc_btn_style.online_content;
                        if (h5Btn) h5Btn.innerText = defaultStyle.h5_btn_style.online_content;
                        // 文案长度>=12
                    } else { // 离线文案变化
                        console.log("isOnline:离线", isOnline, defaultStyle.pc_btn_style.offline_content, defaultStyle.h5_btn_style.offline_content);
                        if (pcBtn) pcBtn.innerText = defaultStyle.pc_btn_style.offline_content;
                        if (h5Btn) h5Btn.innerText = defaultStyle.h5_btn_style.offline_content;
                        // 文案长度>=12
                    }
                    resetBtnIframeClass();
                    break;
                case 'lastMsg': // 客服最新聊天消息content  未读消息数量noRead
                    const b1 = document.querySelector('.preview-box.pc');
                    const b2 = document.querySelector('.preview-box.h5');
                    if (e.data.noRead) { // 未读预览
                        b1.innerText = e.data.content;
                        b2.innerText = e.data.content;
                        if (defaultStyle.pc_btn_style.message_preview === 'off') {
                            console.log(111111111);
                            b1.style.display = "none";
                        } else {
                            console.log(2222222222);
                            b1.style.display = "inline-block";
                        }
                        if (defaultStyle.h5_btn_style.message_preview === 'off') {
                            console.log(3333333);
                            b2.style.display = "none";
                        } else {
                            console.log(4444444);
                            b2.style.display = "inline-block";
                        }
                    } else { // 无未读
                        clearPreviewBox();
                    }
                    break;
            }
        }
    }

    // 更新按钮class
    function resetBtnIframeClass() {
        const pcBtn = document.querySelector('.marquee-content.pc');
        const h5Btn = document.querySelector('.marquee-content.h5');
        const { pc_btn_style, h5_btn_style } = defaultStyle;
        let pcMsg = isOnline ? pc_btn_style.online_content : pc_btn_style.offline_content;
        if (pcMsg.length > 12) { // 长文字
            if (pcBtn) pcBtn.classList.remove('short');
        } else { // 短文字
            if (pcBtn) pcBtn.classList.add('short');
        }

        let h5Msg = isOnline ? h5_btn_style.online_content : h5_btn_style.offline_content;
        if (h5Msg.length > 12) { // 长文字
            if (h5Btn) h5Btn.classList.remove('short');
        } else { // 短文字
            if (h5Btn) h5Btn.classList.add('short');
        }
    }

    // 清空预览
    function clearPreviewBox() {
        const b1 = document.querySelector('.preview-box.pc');
        const b2 = document.querySelector('.preview-box.h5');
        b1.innerText = '';
        b2.innerText = '';
        b1.style.display = "none";
        b2.style.display = "none";
    }

    // 生成style
    function createStyle() {
        let style = document.createElement('style');
        style.innerHTML = `
        /* 按钮 */ 
        .YUNKE-body-btn-9527 {
            z-index: 99999;
            position: fixed;
            border: 0;     
            padding: 0;  
            color: #fff;
            box-sizing: border-box;
            box-shadow: -10px 0px 24px 0px rgba(0, 0, 0, 0.08);
            cursor: pointer;
            font-size: 14px;
        }

        .YUNKE-body-btn-9527 span {
            box-sizing: border-box;
        }

        /* h5 */ 
        @media screen and (max-width: 767px) {
            .YUNKE-body-btn-9527.kefu-btn-bottom-pc,
            .YUNKE-body-btn-9527.kefu-btn-round-pc,
            .YUNKE-body-btn-9527.kefu-btn-side-pc {
                display: none;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-h5 {
                height: 42px;
                display: inline-flex;
                align-items: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-h5>span {
                height: 100%;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-h5>span:nth-of-type(1) {
                padding: 0 12px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border-right: 1px solid #fff;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-h5>span:nth-of-type(1) img {
                width: 26px;
                height: 26px;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-h5>span:nth-of-type(2) {
                padding: 0 16px;
                display: inline-flex;
                align-items: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-round-h5 {
                height: 76px;
                width: 76px;
                border-radius: 50%;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-round-h5 img {
                width: 26px;
                height: 26px;
            }

            .YUNKE-body-btn-9527.kefu-btn-side-h5 {
                width: 42px;
                display: inline-flex;
                flex-direction: column;
                align-items: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-side-h5>span {
                width: 100%;
            }
            .YUNKE-body-btn-9527.kefu-btn-side-h5>span:nth-of-type(1) {
                padding: 12px 0;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border-bottom: 1px solid #fff;
            }
            .YUNKE-body-btn-9527.kefu-btn-side-h5>span:nth-of-type(1) img {
                width: 26px;
                height: 26px;
            }
            .YUNKE-body-btn-9527.kefu-btn-side-h5>span:nth-of-type(2) {
                padding: 10px;
                line-height: 22px;
                display: inline-flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
        }
      
        /* pc */ 
        @media screen and (min-width: 768px) {
            .YUNKE-body-btn-9527.kefu-btn-bottom-h5,
            .YUNKE-body-btn-9527.kefu-btn-round-h5,
            .YUNKE-body-btn-9527.kefu-btn-side-h5 {
                display: none;
            }


            .YUNKE-body-btn-9527.kefu-btn-bottom-pc {
                height: 42px;
                display: inline-flex;
                align-items: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-pc>span {
                height: 100%;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-pc>span:nth-of-type(1) {
                padding: 0 12px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border-right: 1px solid #fff;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-pc>span:nth-of-type(1) img {
                width: 26px;
                height: 26px;
            }

            .YUNKE-body-btn-9527.kefu-btn-bottom-pc>span:nth-of-type(2) {
                padding: 0 16px;
                display: inline-flex;
                align-items: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-round-pc {
                height: 76px;
                width: 76px;
                border-radius: 50%;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-round-pc img {
                width: 26px;
                height: 26px;
            }

            .YUNKE-body-btn-9527.kefu-btn-side-pc {
                width: 42px;
                display: inline-flex;
                flex-direction: column;
                align-items: center;
            }

            .YUNKE-body-btn-9527.kefu-btn-side-pc>span {
                width: 100%;
            }
            .YUNKE-body-btn-9527.kefu-btn-side-pc>span:nth-of-type(1) {
                padding: 12px 0;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border-bottom: 1px solid #fff;
            }
            .YUNKE-body-btn-9527.kefu-btn-side-pc>span:nth-of-type(1) img {
                width: 26px;
                height: 26px;
            }
            .YUNKE-body-btn-9527.kefu-btn-side-pc>span:nth-of-type(2) {
                padding: 10px;
                line-height: 22px;
                display: inline-flex;
                flex-direction: column;
                align-items: center;
                text-align: center;
            }
        }

        /* iframe页面样式 */
        .YUNKE-page-iframe-9527 {
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 99999;
            position: fixed;
            border: 0;
        }

        /* h5 */
        @media screen and (max-width: 767px) {
            .YUNKE-page-iframe-9527 {
                width: 100%;
                height: 100%;
                left: 0;
                top: 0;
                border: 0;     
                padding: 0;  
            }
        }

        /* pc */
        @media screen and (min-width: 768px) {
            .YUNKE-page-iframe-9527 {
                box-shadow: -10px 0px 24px 0px rgba(0, 0, 0, 0.08);
            }

            .YUNKE-page-iframe-9527.fix-pc {
                width: 687px;
                height: 579px;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                border-radius: 10px;
                overflow: hidden;
            }
        }

        /* 跑马灯-横向 */
        @keyframes marqueeAnim-X {
            0% {
                transform: translateX(0px);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        .YUNKE-body-btn-9527.horizontal .marquee-content {
            white-space: nowrap;
            display: inline-block;
            padding: 0 16px;
            color: #fff;
            animation: marqueeAnim-X 30s linear 0s infinite;
        }

        .YUNKE-body-btn-9527.horizontal .marquee-content.short {
            animation: none;
        }

        .YUNKE-body-btn-9527.horizontal .marquee-root {
            max-width: 200px;
            height: 30px;
            text-align: left;
            line-height: 30px;
            overflow: hidden;
        }

        /* 跑马灯-竖直 */
        @keyframes marqueeAnim-Y {
            0% {
                transform: translateY(0px);
            }
            100% {
                transform: translateY(-100%);
            }
        }

        .YUNKE-body-btn-9527.vertical .marquee-content {
            white-space: wrap;
            display: inline-block;
            padding: 10px 16px;
            color: #fff;
            animation: marqueeAnim-Y 30s linear 0s infinite;
        }

        .YUNKE-body-btn-9527.vertical .marquee-content.short {
            animation: none;
        }

        .YUNKE-body-btn-9527.vertical .marquee-root {
            max-height: 260px;
            text-align: left;
            overflow: hidden;
        }

        /* 消息预览 */
        .YUNKE-body-btn-9527 .preview-box {
            display: none;
            font-style: normal;
            position: absolute;
            top: 0%;
            right: 102%;
            max-width: 200px;
            min-width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            background: rgba(243, 245, 249, 1);
            color: rgba(54, 63, 105, 1);
            padding: 0 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            border-radius: 4px;
        }

        .YUNKE-body-btn-9527.kefu-btn-round-h5 .preview-box,
        .YUNKE-body-btn-9527.kefu-btn-round-pc .preview-box {
            top: -50%;
        }

        .YUNKE-body-btn-9527 .preview-box.left,
        .YUNKE-body-btn-9527 .preview-box.left {
            right: initial;
            left: 102%;
        }

        /* 消息预览 h5 */
        @media screen and (max-width: 767px) {
            .YUNKE-body-btn-9527 .preview-box.pc {
                display: none;
            }
        }
       
        /* 消息预览 pc */
        @media screen and (max-width: 767px) {
            .YUNKE-body-btn-9527 .preview-box.h5 {
                display: none;
            }
        }


        
        /* h5 */
        @media screen and (max-width: 767px) {
            /* h5左上角按钮 */
            .yk-h5-left-top {
            
            }


            /* h5左下角按钮 */
            .yk-h5-left-bottom {

            }


            /* h5右上角按钮 */
            .yk-h5-right-top {

            }


            /* h5右下角按钮 */
            .yk-h5-right-bottom {

            }
        }
        
        /* pc */ 
        @media screen and (min-width: 768px) {
            /* pc左上角按钮 */
            .yk-pc-left-top {
            
            }

            /* pc左下角按钮 */
            .yk-pc-left-bottom {

            }

            /* pc右上角按钮 */
            .yk-pc-right-top {

            }

            /* pc右下角按钮 */
            .yk-pc-right-bottom {

            }
        }

    `;
        document.head.appendChild(style);
    }

    // 监听窗口变化
    function listenWinReSize() {
        window.onresize = function () {
            if (ls9627Map.embedModel === 'html') {
                debounce(changeHtmlIframeStyle, 80);
            } else {
                debounce(changeIframeStyle, 80);
            }
        }
    }

    // 防抖
    let dtimer = null;
    function debounce(fn, delay) {
        if (dtimer) clearTimeout(dtimer)
        dtimer = setTimeout(() => {
            fn()
        }, delay)
    }

    // 窗口宽度变化-html模式
    function changeHtmlIframeStyle() {
        if (!ls9627Map.iframeId) return;
        let iframe = document.getElementById(ls9627Map.iframeId);
        if (!iframe) return;

        const width = document.documentElement.clientWidth || document.body.clientWidth;
        if (width > 767) { // pc重置样式
            iframe.classList.add('fix-pc');
        } else { // h5重置样式
            iframe.classList.remove('fix-pc');
        }
    }

    // 窗口宽度变化
    function changeIframeStyle() {
        if (!ls9627Map.iframeId) return;
        let iframe = document.getElementById(ls9627Map.iframeId);
        if (!iframe) return;

        const width = document.documentElement.clientWidth || document.body.clientWidth;
        if (width > 767) { // pc重置样式
            const { location, side_margin, bottom_margin, round_angle, window_width, window_height } = defaultStyle.pc_win_style;

            // 左侧
            if (location == '1') {
                iframe.style.left = (side_margin || 0) + 'px';
                iframe.style.right = 'unset';
            }
            // 右侧
            if (location == '2') {
                iframe.style.left = 'unset';
                iframe.style.right = (side_margin || 0) + 'px';
            };
            // 中间
            if (location == '3') {
                iframe.style.left = '50%';
                iframe.style.right = 'unset';
                iframe.style.transform = 'translateX(-50%)';
            }
            // 圆角
            iframe.style.borderRadius = (round_angle || 0) + 'px';
            // 聊天窗宽度
            iframe.style.width = (window_width || 375) + 'px';
            // 聊天窗高度
            iframe.style.height = (window_height || 592) + 'px';
            iframe.style.bottom = (bottom_margin || 0) + 'px';
        } else { // h5重置样式
            iframe.style.left = '0px';
            iframe.style.right = '0px';
            iframe.style.bottom = '0px';
            iframe.style.transform = 'translateX(0px)';
            iframe.style.borderRadius = '0px';
            iframe.style.width = '100%';
            iframe.style.height = '100%';
        }
    }

    // 跑马灯
    function createTextContent(msg, client, cls) {
        const div1 = document.createElement('div');
        div1.className = 'marquee-root';
        const div2 = document.createElement('div');
        div2.innerText = msg;
        div2.className = `marquee-content ${client} ${cls}`;
        div1.appendChild(div2);
        return div1;
    }

    initYUNKE();
})(window, document);
