-- Set hyper to ctrl + alt + cmd + shift
-- local hyper = {'ctrl', 'cmd', 'alt', 'shift'}
local hyper = {'ctrl', 'alt'}

-- Move Mouse to center of next Monitor 把鼠标移动到下一个屏幕正中间
hs.hotkey.bind('ctrl', '`', function()
    local screen = hs.mouse.getCurrentScreen()
    local nextScreen = screen:next()
    local rect = nextScreen:fullFrame()
    local center = hs.geometry.rectMidPoint(rect)

    hs.mouse.setAbsolutePosition(center)
end)