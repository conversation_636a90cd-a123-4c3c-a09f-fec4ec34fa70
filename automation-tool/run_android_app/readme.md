<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2024-12-05 20:28:21
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2024-12-05 20:32:16
 * @FilePath     : /automation-tool/hammerspoon/run_android_app/readme.md
 * @Description  :
 * Copyright 2024 Bruce, All Rights Reserved.
 * 2024-12-05 20:28:21
-->

# 设置 Mac 双击 Control 键快速运行 Android App

本指南介绍如何在 Mac 上设置一个快捷操作：通过双击 Control 键，自动切换到 Android Studio 并运行应用程序。

## 前置需求

1. 已安装 Android Studio
2. 已安装 Karabiner-Elements

## 设置步骤

### 1. 创建 AppleScript

1. 创建脚本目录：`~/scripts`
2. 创建并编辑运行脚本：`run_android.scpt`
3. 添加脚本执行权限

### 3. 配置 Karabiner-Elements

1. 打开 Karabiner-Elements 配置文件
2. 在 .config/karabiner/karabiner.json 中

## 使用方法

- 快速双击左 Control 键
- 系统会自动切换到 Android Studio 并运行应用

## 注意事项

- 确保 Android Studio 已经打开
- 双击间隔不要太长
- 首次使用可能需要重启 Karabiner-Elements

## 故障排除

- 如果快捷键不响应，检查 Karabiner-Elements 权限
- 如果切换到 Android Studio 但未运行应用，检查脚本权限
- 配置文件修改后需要重新加载 Karabiner-Elements
