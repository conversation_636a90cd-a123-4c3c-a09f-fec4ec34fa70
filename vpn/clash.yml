
rules:
  # USA 代理组， 全部是美国节点
  - DOMAIN,labs.google,USA
  - DOMAIN,learning.google,USA
  - DOMAIN-SUFFIX,bard.google.com,USA
  - DOMAIN-SUFFIX,gemini.google.com,USA
  - DOMAIN-SUFFIX,learning.google.com,USA
  - DOMAIN-SUFFIX,aistudio.google.com,USA
  - DOMAIN-SUFFIX,docs.google.com,USA
  - DOMAIN-SUFFIX,chat.openai.com,USA
  - DOMAIN-SUFFIX,openai.com,USA
  - DOMAIN-SUFFIX,chatgpt.com,USA
  - DOMAIN-SUFFIX,anthropic.com,USA
  - DOMAIN-SUFFIX,claude.ai,USA
  # Fast 代理组，日本，韩国，新加坡, 主要是快, 能够满足日常翻墙就行, 选择速率最快的节点
  - DOMAIN-SUFFIX,cursor.sh,Fast
  - DOMAIN-KEYWORD,cursor,Fast
  - DOMAIN-SUFFIX,x.com,Fast
  - DOMAIN-SUFFIX,x.ai,Fast
  - DOMAIN-SUFFIX,amazonaws.com,Fast
  - DOMAIN-SUFFIX,api.augmentcode.com,Fast
  - DOMAIN-SUFFIX,azure.com,Fast
  - DOMAIN-SUFFIX,microsoft.com,Fast
  - DOMAIN-SUFFIX,windows.net,Fast
  - DOMAIN-SUFFIX,office.com,Fast
  - DOMAIN-SUFFIX,office365.com,Fast
  - DOMAIN-SUFFIX,office365.us,Fast
  - DOMAIN-SUFFIX,office365.us,Fast
  - DOMAIN-SUFFIX,github.com,Fast
  - DOMAIN-SUFFIX,api.github.com,Fast
  - DOMAIN-SUFFIX,youtube.com,Fast
  - DOMAIN-SUFFIX,marscode.com,Fast
  - DOMAIN-SUFFIX,codeium.com,Fast
  - DOMAIN-SUFFIX,twimg.com,Fast
  - DOMAIN,www.google.com,Fast
  # 一些常用的国内域名,需要走直连  DIRECT
  - DOMAIN-SUFFIX,weixin.qq.com,DIRECT
  - DOMAIN-SUFFIX,weixin.com,DIRECT
  - DOMAIN-SUFFIX,qq.com,DIRECT
  - DOMAIN-SUFFIX,aliyun.com,DIRECT
  - DOMAIN-SUFFIX,baidu.com,DIRECT
  - DOMAIN-SUFFIX,sina.com.cn,DIRECT
  - DOMAIN-SUFFIX,163.com,DIRECT
  - DOMAIN-SUFFIX,sogou.com,DIRECT
  - DOMAIN-SUFFIX,toutiao.com,DIRECT
  - DOMAIN-SUFFIX,zhihu.com,DIRECT
  - DOMAIN-SUFFIX,taobao.com,DIRECT
  - DOMAIN-SUFFIX,tmall.com,DIRECT
  - DOMAIN-SUFFIX,jd.com,DIRECT
  - DOMAIN-SUFFIX,pinduoduo.com,DIRECT
  - DOMAIN-SUFFIX,dangdang.com,DIRECT
  - DOMAIN-SUFFIX,weibo.com,DIRECT
  - DOMAIN-SUFFIX,douyin.com,DIRECT
  - DOMAIN-SUFFIX,doubao.com,DIRECT
  - DOMAIN-SUFFIX,deepseek.com,DIRECT
  - DOMAIN-SUFFIX,kuaishou.com,DIRECT
  - DOMAIN-SUFFIX,bilibili.com,DIRECT
  - DOMAIN-SUFFIX,xiaohongshu.com,DIRECT
  - DOMAIN-SUFFIX,alipay.com,DIRECT
  - DOMAIN-SUFFIX,tenpay.com,DIRECT
  - DOMAIN-SUFFIX,mi.com,DIRECT
  - DOMAIN-SUFFIX,huawei.com,DIRECT
  - DOMAIN-SUFFIX,siliconflow.cn,DIRECT
  # Keyword 直连
  - DOMAIN-KEYWORD,deepseek,DIRECT
  - DOMAIN-KEYWORD,taobao,DIRECT
  - DOMAIN-KEYWORD,weixin,DIRECT
  - DOMAIN-KEYWORD,qq,DIRECT
  - DOMAIN-KEYWORD,tencent,DIRECT
  - DOMAIN-KEYWORD,tongyi,DIRECT
  # 大陆的 ip 直接走直连
  - GEOIP,CN,DIRECT

# 下面是在 clash verge 里面添加的规则示例，按照下面的格式添加即可
prepend:
  - "DOMAIN-SUFFIX,bard.google.com,AI"
  - "DOMAIN-SUFFIX,gemini.google.com,AI"
  - "DOMAIN-SUFFIX,learning.google.com,AI"
  - "DOMAIN-SUFFIX,chat.openai.com,AI"
  - "DOMAIN-SUFFIX,openai.com,AI"
  - "DOMAIN-SUFFIX,chatgpt.com,AI"
  - "DOMAIN-SUFFIX,anthropic.com,AI"
  - "DOMAIN-SUFFIX,claude.ai,AI"
  - "DOMAIN-SUFFIX,github.com,Fast"
  - "DOMAIN-SUFFIX,api.github.com,Fast"
  - "DOMAIN-SUFFIX,weixin.qq.com,DIRECT"
  - "DOMAIN-SUFFIX,weixin.com,DIRECT"
  - "DOMAIN-SUFFIX,qq.com,DIRECT"
  - "DOMAIN-KEYWORD,tongyi,DIRECT"
  - "DOMAIN-SUFFIX,aliyun.com,DIRECT"
  - "DOMAIN-SUFFIX,baidu.com,DIRECT"
  - "DOMAIN-SUFFIX,google.com,Fast"
append: []
delete: []




# Clashx Pro 中自定义
rules:
  # AI 代理组，必须选中美国节点
  - DOMAIN,labs.google,🤖 OpenAi
  - DOMAIN,learning.google,🤖 OpenAi
  - DOMAIN-SUFFIX,bard.google.com,🤖 OpenAi
  - DOMAIN-SUFFIX,gemini.google.com,🤖 OpenAi
  - DOMAIN-SUFFIX,learning.google.com,🤖 OpenAi
  - DOMAIN-SUFFIX,aistudio.google.com,🤖 OpenAi
  - DOMAIN-SUFFIX,docs.google.com,🤖 OpenAi
  - DOMAIN-SUFFIX,x.com,🤖 OpenAi
  - DOMAIN-SUFFIX,x.ai,🤖 OpenAi
  - DOMAIN-SUFFIX,chat.openai.com,🤖 OpenAi
  - DOMAIN-SUFFIX,openai.com,🤖 OpenAi
  - DOMAIN-SUFFIX,chatgpt.com,🤖 OpenAi
  - DOMAIN-SUFFIX,anthropic.com,🤖 OpenAi
  - DOMAIN-SUFFIX,claude.ai,🤖 OpenAi
  - DOMAIN-SUFFIX,marscode.com,🤖 OpenAi
  - DOMAIN-SUFFIX,cursor.sh,🤖 OpenAi
  # Telegram 代理组, 能够满足日常翻墙就行, 选择速率最快的节点
  - DOMAIN-SUFFIX,github.com,📲 Telegram
  - DOMAIN-SUFFIX,api.github.com,📲 Telegram
  - DOMAIN-SUFFIX,youtube.com,📲 Telegram
  - DOMAIN-SUFFIX,codeium.com,📲 Telegram
  - DOMAIN-SUFFIX,twimg.com,📲 Telegram
  - DOMAIN,www.google.com,📲 Telegram
  # 一些常用的国内域名,需要走直连  DIRECT
  - DOMAIN-SUFFIX,weixin.qq.com,DIRECT
  - DOMAIN-SUFFIX,weixin.com,DIRECT
  - DOMAIN-SUFFIX,qq.com,DIRECT
  - DOMAIN-SUFFIX,aliyun.com,DIRECT
  - DOMAIN-SUFFIX,baidu.com,DIRECT
  - DOMAIN-SUFFIX,sina.com.cn,DIRECT
  - DOMAIN-SUFFIX,163.com,DIRECT
  - DOMAIN-SUFFIX,sogou.com,DIRECT
  - DOMAIN-SUFFIX,toutiao.com,DIRECT
  - DOMAIN-SUFFIX,zhihu.com,DIRECT
  - DOMAIN-SUFFIX,taobao.com,DIRECT
  - DOMAIN-SUFFIX,tmall.com,DIRECT
  - DOMAIN-SUFFIX,jd.com,DIRECT
  - DOMAIN-SUFFIX,pinduoduo.com,DIRECT
  - DOMAIN-SUFFIX,dangdang.com,DIRECT
  - DOMAIN-SUFFIX,weibo.com,DIRECT
  - DOMAIN-SUFFIX,douyin.com,DIRECT
  - DOMAIN-SUFFIX,kuaishou.com,DIRECT
  - DOMAIN-SUFFIX,bilibili.com,DIRECT
  - DOMAIN-SUFFIX,xiaohongshu.com,DIRECT
  - DOMAIN-SUFFIX,alipay.com,DIRECT
  - DOMAIN-SUFFIX,tenpay.com,DIRECT
  - DOMAIN-SUFFIX,mi.com,DIRECT
  - DOMAIN-SUFFIX,huawei.com,DIRECT
  - DOMAIN-SUFFIX,siliconflow.cn,DIRECT
  # Keyword 直连
  - DOMAIN-KEYWORD,deepseek,DIRECT
  - DOMAIN-KEYWORD,taobao,DIRECT
  - DOMAIN-KEYWORD,weixin,DIRECT
  - DOMAIN-KEYWORD,qq,DIRECT
  - DOMAIN-KEYWORD,tencent,DIRECT
  - DOMAIN-KEYWORD,tongyi,DIRECT
  # 大陆的 ip 直接走直连
  - GEOIP,CN,DIRECT