{"profiles": [{"complex_modifications": {"rules": [{"description": "光标四方向移动、翻页", "enabled": false, "manipulators": [{"from": {"key_code": "u", "modifiers": {"mandatory": ["left_option"], "optional": ["any"]}}, "to": [{"key_code": "page_up"}], "type": "basic"}, {"from": {"key_code": "d", "modifiers": {"mandatory": ["left_option"], "optional": ["any"]}}, "to": [{"key_code": "page_down"}], "type": "basic"}, {"from": {"key_code": "h", "modifiers": {"mandatory": ["left_option"], "optional": ["any"]}}, "to": [{"key_code": "left_arrow"}], "type": "basic"}, {"from": {"key_code": "l", "modifiers": {"mandatory": ["left_option"], "optional": ["any"]}}, "to": [{"key_code": "right_arrow"}], "type": "basic"}, {"from": {"key_code": "j", "modifiers": {"mandatory": ["left_option"], "optional": ["any"]}}, "to": [{"key_code": "down_arrow"}], "type": "basic"}, {"from": {"key_code": "k", "modifiers": {"mandatory": ["left_option"], "optional": ["any"]}}, "to": [{"key_code": "up_arrow"}], "type": "basic"}]}, {"description": "CapsLock 组合键时转换为 Control", "manipulators": [{"from": {"key_code": "caps_lock", "modifiers": {"optional": ["any"]}}, "to": [{"key_code": "left_control"}], "to_if_alone": [{"hold_down_milliseconds": 300, "key_code": "caps_lock"}], "type": "basic"}]}, {"description": "长按 Ctrl run Android app", "manipulators": [{"type": "basic", "from": {"key_code": "left_control", "modifiers": {"mandatory": []}}, "parameters": {"basic.to_if_held_down_threshold_milliseconds": 500}, "to": [{"key_code": "left_control"}], "to_if_held_down": [{"shell_command": "osascript ~/scripts/run_android.scpt"}]}]}]}, "name": "Default profile", "selected": true, "virtual_hid_keyboard": {"keyboard_type_v2": "iso"}}]}