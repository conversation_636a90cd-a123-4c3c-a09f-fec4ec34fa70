<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-28 18:48:33
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-03-28 21:20:53
 * @FilePath     : /jianli.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-03-28 18:48:33
-->

#### 青峰

##### 技术体系

- 移动开发：Swift、Flutter、React Native、Android
- 前端框架：React+TS、Vue3、Next.js、Canvas/SVG
- 工程架构：微前端架构、CI-CD、性能优化、模块化
- 跨平台：Flutter、Electron、Tauri
- 后端协同：Node.js、Python、Ruby、RESTful API
- 工具与生态：Git、Docker、Jenkins、WebGL、 Webpack/Vite、WebSocket

---

##### 工作经历

###### 招商银行 （2012-2015）

iOS 工程师

- 参与金融类 App 架构演进，Objective-C 向 Swift 的技术迁移，完成核心模块的现代化改造
- 主导 App 性能监控系统建设，关键指标可视化覆盖率 100%，为性能优化提供数据支撑

###### MC Tec （2015-2018）

iOS/Web 全栈工程师

- 推动 Web 与 Native 技术融合，落地 Hybrid 开发规范
- 设计研发通用业务组件库，覆盖 UI、网络请求、异常处理等场景，跨项目复用率达 80%+
- 建立前端工程化体系，构建效率提升 3 倍

###### 京东集团（2019-2021）

移动端架构师

- 主导移动端架构升级，落地 React Native 混合开发方案，优化启动速度与渲染性能，支撑千万级用户访问
- 构建企业级微前端解决方案，支撑 10+子应用并行开发，降低耦合度与维护成本
- 创新实现 Web 与 Native 双向通信引擎，采用 WebSocket+长轮询混合模式，保障实时性与稳定性
- 设计统一的 UI 组件库，覆盖移动端与桌面端，实现一次开发、多端部署
- 低代码平台开发，设计组件化、可视化的开发工具，赋能非技术人员快速搭建页面

###### 当前（2021-2025）

跨平台技术负责人

- 制定 Web+Native 混合开发技术标准，实现 H5 模块动态加载
- 设计模块化插件架构，支持按需加载与热更新
- 桌面应用架构设计: 设计 Electron 桌面应用架构方案， 开发实现 Windows/macOS 双平台应用，兼容性达 99.9%
- 构建跨平台通信桥接方案，支持 H5、Flutter 和 Native 的无缝交互，数据一致性达到 100%。
- 引入 WebGL 技术，为桌面端应用提供高性能的数据可视化能力

---

##### 项目成就

###### 招行掌上生活 iOS

技术架构：Swift+Objective-C 混合开发

- 实现动态化模块加载方案，发版周期缩短 30%，提升敏捷开发能力
- 设计内存泄漏监控方案，保障 App 长期稳定运行
- 成果：App Store 年度金融类应用 TOP10，日活用户突破 1000 万，月流水超 10 亿

###### 美团生活服务平台

技术组合：JS+React+iOS+Node.js

- 实时预约系统，基于 WebSocket 实现毫秒级响应，支持多端同步，预约成功率提升 40%
- Native 与 Web 双向通信方案，确保数据一致性与高效交互
- 会员管理系统，整合电子签名、二维码核销等功能，优化线下服务体验

###### 企业微信应用平台

技术方案：React+微前端架构

- 开发营销组件库，支持商家快速搭建个性化页面，提升 50% 运营效率, 支持 20+活动模板快速搭建
- 实现多租户隔离方案，保证数据安全，支撑 1000+商家接入
- 构建实时数据看板 构建 BI 分析系统，提供关键指标可视化

###### 智能客服系统

技术架构：Ruby+Vue+WebSocket+Node.js

- 实现消息队列分级处理机制，保障核心业务消息优先触达
- 构建智能路由策略，结合用户画像与 AI 算法，实现专属客服匹配，客户满意度提升 50%
- 消息聊天组件支持富文本、图片、文件传输等功能，提升用户互动体验
- 实现多渠道消息归一化处理，支持网站/Telegram/Email 统一接入
- 集成 OpenAI 智能会话与 Google 翻译服务，打造全球化客服解决方案
- 构建客服 KPI 数据看板，关键指标可视化覆盖率 100%

---

##### 教育背景

中国农业大学 | 计算机科学与技术 | 本科
