<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-03-28 21:22:54
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-03-28 21:26:45
 * @FilePath     : /pan.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-03-28 21:22:54
-->

# 琮珞 个人优势

11年移动端&前端架构经验，技术横跨iOS(Swift/ObjC)、Android(Kotlin)、Flutter、Vue3全生态，具备从底层SDK开发到大型分布式系统架构的全链路能力

- 精通Swift高级特性(Property Wrapper、Actor并发模型)
- 深度掌握Metal性能优化技术，实现图形渲染性能提升300%
- 构建企业级组件库被Apple Store Top 100应用采用
- 设计Flutter混合开发框架，代码复用率达90%+，编译速度优化40%
- 开发动态化引擎支持热更新覆盖率95%，获公司年度技术创新奖
- 打造微前端监控体系，错误捕捉率99.9%，MTTR缩短至15分钟
- 突破性实现iOS启动时间<400ms

## 工作经历

### 当前 | 2023.09-至今

- **主导前端架构设计**：采用Vue3+TypeScript+Pinia现代化技术栈，实现首屏加载速度优化42%(Lighthouse评分从68→92)
- **开发高性能组件库**：基于Headless UI理念设计WootUIKit，支持20+可配置组件，被3个业务线复用
- **设计双通道数据同步策略**(REST+WebSocket)，消息到达时间 <200ms
- **开发乐观更新中间件**，冲突解决成功率100%
- **搭建Vite+Micro Frontends微前端架构**，模块热更新速度提升65%
- **实现自动化代码审计**：集成ESLint+SonarQube，代码异味减少80%

### 北京福里科技有限公司 | 2019.09-2023.09

- **主导完成跨平台架构升级**：采用Flutter重构核心模块，降低双端开发成本40%
- **实现SwiftUI+Combine现代化技术栈迁移**，View代码量减少35%
- **建立自动化质量体系**：集成UnitTest覆盖率提升至80%，UI自动化测试覆盖核心场景

### 北京开天创世科技有限公司 | 2019.03-2019.09

- **设计实现低延迟投屏协议**：基于WebSocket自定义二进制协议，操作延迟<50ms
- **开发高性能绘图引擎**：利用Metal框架实现60fps手写批注，支持万级矢量图形渲染

### 文思海辉技术有限公司 | 2013.10-2019.03

- **构建金融级安全体系**：整合CFCA国密算法、活体检测(误识率<0.01%)、防截屏等安全方案
- **开发高复用组件库**：封装OCR识别组件(识别准确率99.2%)、动态表单生成器等20+基础模块

## 项目经历

### 智能客服系统 | 2023.09-至今

**主导现代化技术栈升级**：基于 Vue 3 + TypeScript + Pinia 构建高性能前端架构

1. 首屏加载性能优化提升 42% (Lighthouse 评分从 68 提升至 92)
2. 设计实现双通道数据同步机制，消息实时性 <200ms
3. 开发乐观更新中间件，实现无感知并发冲突解决
4. 采用 Headless UI 设计理念，提供 20+ 高度可配置组件
5. 构建微前端架构和自动化工具链
6. 基于 Vite + Micro Frontends 实现模块独立开发部署
7. 集成 ESLint + SonarQube 自动化代码审计，代码质量提升 80%

### 柏润学堂 | 2023.05-2023.09

**打造企业级移动学习平台**，实现培训全流程数字化，日活用户 5000+

**架构设计**:
1. 采用 MVVM + Clean Architecture 分层架构
2. 使用 Swift 5.0 + Combine 实现响应式编程
3. 基于 Core Data 设计离线课程缓存系统
4. 自研组件化框架，模块间解耦率达 95%

**视频播放系统**:
1. 基于 AVFoundation 自研播放器内核
2. 支持后台播放和画中画功能
3. 实现预加载和智能缓存策略
4. 视频加密存储，确保课程内容安全

**优化播放性能**:
1. 首帧加载时间 <300ms
2. 内存占用较标准播放器降低 40%

**安全防作弊系统**:
1. 集成自研人脸识别 SDK，识别准确率 99.5%
2. 实现连续人脸追踪，确保考试真实性
3. 实现 App 级别截屏防护

### 酷连 | 2020.10-2023.05

**设计低延迟投屏核心协议**:
1. 基于 WebSocket 实现自定义二进制协议，操作延迟控制在 50ms 以内
2. 支持多设备同时连接，并实现自动负载均衡

**实现跨平台重构**:
1. 采用 Flutter 重构核心功能模块，实现 85% 代码复用
2. 优化启动性能，冷启动时间降低 60%

**打造自动化测试体系**:
1. 单元测试覆盖率提升至 80%
2. 核心业务流程实现 UI 自动化测试

### 共享屏 | 2020.05-2020.10

**开发高性能绘图引擎**:
1. 基于 Metal 框架实现 60fps 流畅手写批注
2. 支持万级矢量图形实时渲染
3. 优化内存占用，降低 50% GPU 使用率

**设计多端协同编辑方案**:
1. 实现实时协作白板，支持多人同时编辑
2. 采用 OT 算法处理并发冲突

### 富元环球(社区) | 2019.10-2020.03

**负责打造面向香港市场的高性能股票交易 App**，日活用户超过 10 万，为用户提供一站式证券投资服务平台。

**社交系统架构设计与实现**:
1. 设计并实现基于环信 IM 的实时社区系统，支持文本、图片等多媒体消息
2. 优化消息同步机制，实现离线消息秒级同步，消息送达率达 99.9%
3. 开发智能消息防重复机制，解决网络波动导致的消息重复问题

**性能优化**:
1. 重构社区列表架构，采用 Cell 重用池 + 异步图片加载方案
2. 实现列表数据预加载和缓存策略，滑动帧率提升至稳定 60fps
3. 优化内存占用，降低 50% 图片缓存内存

**技术创新**:
1. 设计图片压缩算法，在保证显示质量的同时将上传流量减少 70%
2. 开发自定义表情包系统，支持动态表情资源更新
3. 实现消息加密传输，保障用户隐私安全

**工程效能提升**:
1. 封装通用社交组件库，提升团队开发效率 40%
2. 建立模块化开发规范，显著降低代码耦合度
3. 引入 Crash 监控系统，应用稳定性提升 35%

### 中国银行手机银行 | 2013.10-2019.03

**构建金融级安全架构**:
1. 整合 CFCA 国密算法、活体检测(误识率 <0.01%)
2. 实现防截屏、设备指纹等多重安全防护

**开发高性能交易系统**:
1. 设计行情推送引擎，支持千万级数据实时更新
2. 优化交易链路，订单处理延迟 <100ms

**封装业务组件库**:
1. 实现 OCR 识别组件(准确率 99.2%)
2. 开发可配置化动态表单生成器
3. 封装 20+ 核心业务组件，提升团队开发效率 40%

## 教育经历

**中国石油大学** | 本科 | 计算机科学与技术 | 2013.10-2019.03
