
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta
  name="viewport"
  content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
/>
  <title>云客客服系统</title>
</head>

<body>
<script type='text/javascript'>
function parse(query) {
  var qs = {};
  var i = query.indexOf('?');
  if (i < 0 && query.indexOf('=') < 0) {
    return qs;
  } else if (i >= 0) {
    query = query.substring(i + 1);
  }
  var parts = query.split('&');
  for (var n = 0; n < parts.length; n++) {
    var part = parts[n];
    var key = part.split('=')[0];
    var val = part.split('=')[1];
    key = key.toLowerCase();
    if (typeof qs[key] === 'undefined') {
      qs[key] = decodeURIComponent(val);
    } else if (typeof qs[key] === 'string') {
      var arr = [qs[key], decodeURIComponent(val)];
      qs[key] = arr;
    } else {
      qs[key].push(decodeURIComponent(val));
    }
  }
  return qs;
}
    (function (a, b, c, d, e, j, s) {
        a[d] = a[d] || function() {
            (a[d].a = a[d].a || []).push(arguments);
        };
        j = b.createElement(c),
            s = b.getElementsByTagName(c)[0];
        j.async = true;
        j.charset = 'UTF-8';
        j.src = 'https://script.chatsuser.com/loadDemo.js';
        s.parentNode.insertBefore(j, s);
    })(window, document, 'script', '_YUNKE');
    var data = parse(window.location.search);
    let ifHidden = data.hiddenclose === 'false' ? false : true;
    _YUNKE({entId: '530444', channelId: '4782858022', language: data.language || 'zh_CN', hiddenClose: ifHidden, embedModel: 'html', ...data});
</script>
</body>

</html>