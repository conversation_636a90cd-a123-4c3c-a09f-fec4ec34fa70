#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工作日报生成脚本

使用方式:
  python daily_report.py                  # 生成今天的日报
  python daily_report.py -a "作者名"      # 指定作者名
  python daily_report.py -d "日期表达式"  # 指定日期
  python daily_report.py -a "作者名" -d "日期表达式"  # 同时指定作者和日期

日期表达式支持的格式:
  1. 标准日期: "2025-07-18"
  2. 简短日期: "7-18" (自动添加当前年份)
  3. 相对天数: "-N" (如 "-1" 表示昨天, "-5" 表示5天前)

输出文件位置:
  ./reports/daily_report_日期.md
"""

import os
import re
import sys
import argparse
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

# 设置项目路径列表
PROJECT_PATHS = [
    "/Users/<USER>/Desktop/bet/betfuguweb",
    "/Users/<USER>/Desktop/customer_sys/website/talka.tw",
    "/Users/<USER>/Desktop/customer_sys/chatwoot"
]

# 设置报告输出路径
REPORT_DIR = os.path.expanduser("~/Desktop/tools-productivity/reports")
os.makedirs(REPORT_DIR, exist_ok=True)

def run_command(cmd, cwd=None):
    """运行命令并返回输出"""
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError:
        return ""

def get_git_author():
    """
    尝试自动获取 Git 用户名（优化顺序）
    1. 首先尝试从项目配置获取
    2. 然后从提交历史中获取
    3. 尝试获取全局 Git 用户名
    4. 尝试使用系统用户名
    5. 如果都失败了，使用默认值
    """
    print("正在尝试获取 Git 用户名...", file=sys.stderr)

    # 1. 首先尝试从项目配置获取
    for project in PROJECT_PATHS:
        git_dir = os.path.join(project, ".git")
        if os.path.isdir(git_dir):
            print(f"检查项目目录: {project}", file=sys.stderr)

            # 尝试获取项目的本地 Git 用户名
            local_name = run_command("git config user.name", cwd=project)
            if local_name:
                print(f"在项目 {os.path.basename(project)} 中找到 Git 用户名: {local_name}", file=sys.stderr)
                return local_name

            print(f"在项目 {os.path.basename(project)} 中没有找到本地 Git 用户名", file=sys.stderr)

            # 2. 尝试从提交历史中获取
            print("尝试从项目提交历史中获取用户名...", file=sys.stderr)
            last_author = run_command("git log -1 --pretty=format:\"%an\"", cwd=project)
            if last_author:
                print(f"从项目提交历史中找到用户名: {last_author}", file=sys.stderr)
                return last_author

            print("项目中没有提交记录或无法获取作者名", file=sys.stderr)

    # 3. 尝试获取全局 Git 用户名
    print("尝试获取全局 Git 用户名...", file=sys.stderr)
    global_name = run_command("git config --global user.name")
    if global_name:
        print(f"找到全局 Git 用户名: {global_name}", file=sys.stderr)
        return global_name

    print("没有找到全局 Git 用户名", file=sys.stderr)

    # 4. 尝试使用系统用户名
    print("尝试使用系统用户名...", file=sys.stderr)
    whoami_name = run_command("whoami")
    if whoami_name:
        print(f"使用系统用户名作为备选: {whoami_name}", file=sys.stderr)
        return whoami_name

    # 5. 如果都失败了，使用默认值
    print("所有检测方法都失败，将使用默认用户名", file=sys.stderr)
    return "Bruce"  # 使用 Bruce 作为默认值

def convert_date(input_date):
    """
    转换日期表达式为具体日期
    支持格式：
    1. 标准日期 "2025-07-18"
    2. 简短日期 "7-18"
    3. 相对天数 "-N"
    """
    today = datetime.now()

    # 处理"-N"格式，表示N天前
    negative_days_match = re.match(r'^-(\d+)$', input_date)
    if negative_days_match:
        days = int(negative_days_match.group(1))
        return (today - timedelta(days=days)).strftime("%Y-%m-%d")

    # 处理简短日期格式 (如 "7-18")
    short_date_match = re.match(r'(\d{1,2})-(\d{1,2})$', input_date)
    if short_date_match:
        month = int(short_date_match.group(1))
        day = int(short_date_match.group(2))
        # 添加当前年份
        try:
            date_obj = datetime(today.year, month, day)
            return date_obj.strftime("%Y-%m-%d")
        except ValueError:
            print("错误：日期格式不正确", file=sys.stderr)
            return None

    # 尝试解析完整日期格式 (如 "2025-07-18")
    try:
        # 首先尝试 YYYY-MM-DD 格式
        date_obj = datetime.strptime(input_date, "%Y-%m-%d")
        return date_obj.strftime("%Y-%m-%d")
    except ValueError:
        # 如果失败，可能是其他格式
        pass

    # 如果以上都不匹配，返回原始输入
    return input_date

def get_git_commits(project_path, author, date):
    """获取指定项目、作者和日期的Git提交记录"""
    if not os.path.isdir(project_path):
        print(f"警告: 项目路径不存在: {project_path}", file=sys.stderr)
        return ""

    cmd = (
        f'git log --author="{author}" '
        f'--since="{date} 00:00:00" --until="{date} 23:59:59" '
        f'--date=format:"%H:%M:%S" --pretty=format:"- %H | %ad | %s" --all'
    )

    return run_command(cmd, cwd=project_path)

def generate_report(author, date):
    """生成工作日报"""
    report_file = os.path.join(REPORT_DIR, f"daily_report_{date}.md")
    have_commits = False

    with open(report_file, 'w', encoding='utf-8') as f:
        # 创建日报文件头部
        f.write(f"# {date} 工作日报\n\n")
        f.write("## Git 提交记录\n\n")

        # 遍历所有项目路径
        for project_path in PROJECT_PATHS:
            if not os.path.isdir(project_path):
                print(f"警告: 项目路径不存在: {project_path}", file=sys.stderr)
                continue

            # 获取项目名称
            project_name = os.path.basename(project_path)

            f.write(f"### 项目: {project_name}\n")
            f.write(f"### 作者: {author}\n\n")

            # 获取指定日期的git提交记录
            commits = get_git_commits(project_path, author, date)

            if not commits:
                f.write(f"该项目在 {date} 没有提交记录。\n")
            else:
                f.write(f"{commits}\n")
                have_commits = True

            f.write("\n")

        # 工作总结部分
        f.write("## 工作内容总结\n\n")

        if have_commits:
            f.write("根据提交记录，当日主要完成了以下工作：\n\n")
        else:
            f.write("当日无 Git 提交记录，请手动填写工作内容：\n\n")

        f.write("1. (在此手动填写工作内容)\n\n")
        f.write("## 明日计划\n\n")
        f.write("1. (在此手动填写明日计划)\n")

    print(f"日报已生成: {report_file}")
    print(f"使用的作者名: {author}")
    print(f"查询的日期: {date}")

    return report_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='生成工作日报')
    parser.add_argument('-a', '--author', help='作者名称')
    parser.add_argument('-d', '--date', help='日期表达式 (YYYY-MM-DD, MM-DD, -N)')

    args = parser.parse_args()

    # 获取作者名
    author = args.author if args.author else get_git_author()

    # 获取日期
    today = datetime.now().strftime("%Y-%m-%d")
    if args.date:
        converted_date = convert_date(args.date)
        if converted_date:
            date = converted_date
        else:
            print("错误：无法解析日期表达式，将使用今天的日期", file=sys.stderr)
            date = today
    else:
        date = today

    # 生成报告
    generate_report(author, date)

if __name__ == "__main__":
    main()