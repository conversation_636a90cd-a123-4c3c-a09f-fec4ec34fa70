<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2022-06-07 14:58:33
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-18 10:24:35
 * @FilePath     : /tools-apps/iterm2.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2022-06-07 14:58:33
-->
##### zsh

---

#### 安装

- 下载 oh-my-zsh
  `git clone https://github.com/robbyrussell/oh-my-zsh.git ~/.oh-my-zsh`

- 复制 .zshrc
  `cp ~/.oh-my-zsh/templates/zshrc.zsh-template ~/.zshrc`

- 更改你的默认 Shell
  `chsh -s /bin/zsh`

- 重启 iterm

`vim ~/.zshrc`

###### 常用插件

- 安装插件的方法
  `1.把插件仓库克隆到 ~/.oh-my-zsh/custom/plugins`
  `2. 在~/.zshrc文件中 plugins=(git zsh-autosuggestions)`
  `3.source ~/.zshrc`
- `git/autojump/incr/zsh-autosuggestions/zsh-syntax-highlighting`

- zsh-syntax-highlighting 语法高亮

`用了 zsh-syntax-highlighting 插件后，粘贴会比较慢，加上下面这段代码可解决`

```
pasteinit() {
 OLD_SELF_INSERT=${${(s.:.)widgets[self-insert]}[2,3]}
 zle -N self-insert url-quote-magic # I wonder if you'd need `.url-quote-magic`?
}
```

- vi-mode 终端 vim 模式

  ` #vi-mode 插件 光标样式

      bindkey -v
      export KEYTIMEOUT=1

      # Change cursor shape for different vi modes

      function zle-keymap-select {
      if [[${KEYMAP} == vicmd]] ||
      [[$1 = 'block']]; then
      echo -ne '\e[1 q'
      elif [[${KEYMAP} == main]] ||
      [[${KEYMAP} == viins]] ||
      [[${KEYMAP} = '']] ||
      [[$1 = 'beam']]; then
      echo -ne '\e[5 q'
      fi
      }
      zle -N zle-keymap-select
      zle-line-init() {
      zle -K viins # initiate `vi insert` as keymap (can be removed if `bindkey -V` has been set elsewhere)
      echo -ne "\e[5 q"
      }
      zle -N zle-line-init
      echo -ne '\e[5 q' # Use beam shape cursor on startup.
      preexec() { echo -ne '\e[5 q' ;} # Use beam shape cursor for each new prompt.

`

- colored-man-pages 自带插件 <man 帮助 高亮>
- extract 自带插件 <解压任何文件>
  `x filename` 即可解压文件
- tig 比 git 更友好的插件
- z 自带插件 <使用 z 命令即可快速跳转目录>
- zsh-autosuggestions 命令提示
- autojump 快速跳转目录
  在~/.zshrc 中添加
  `[[ -s $(brew --prefix)/etc/profile.d/autojump.sh ]] && . $(brew --prefix)/etc/profile.d/autojump.sh`

[安装 brew](https://www.freesion.com/article/4925523755/)

### 终端 json 格式化

    1. npm install -g json
    2. curl https://us-central1-web-sas.cloudfunctions.net/getDeviceList | json

### iTerm2 同步配置

- [方法一](https://juejin.cn/post/7024533887476826143)
- [方法二](https://www.jianshu.com/p/c251d26374c5)

macos键盘鼠标速率
defaults write NSGlobalDomain InitialKeyRepeat -int 10
defaults write NSGlobalDomain KeyRepeat -int 1
defaults write -g com.apple.mouse.scaling 3.0
