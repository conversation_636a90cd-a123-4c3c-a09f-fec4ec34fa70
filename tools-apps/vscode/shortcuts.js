// alt + [ ] 左右页签
// alt + , . 最近编辑的标签
// alt + m 跳转到方法, alt + o
// cmd + alt + [ ] 折叠/展开代码, 多次按此快捷键可以向上级查找折叠

// alt + e 跳转到目录，再按方向键可以选择文件，然后 l 键可以打开文件
// ctrl + e, ctrl + y 滚动屏幕
// 代码折叠 zc , 代码展开 zo, 折叠以后鼠标如果想往下走, 按 \

/**
 *   多光标操作
 * 1. 使用鼠标:  在键盘上按住 “Option” 键，然后用鼠标点击想要插入光标的位置，就可以插入多个光标了。
 * 2. 使用键盘: 首先你还是先移动光标到第一个“5”的前面。然后按下 “Cmd + Option + 下方向键”（Windows 上是 “Ctrl + Alt + 下方向键”），在当前光标的下面创建一个光标。
 * 3. 使用快捷键: CMD + D, 选中相同词
 */
