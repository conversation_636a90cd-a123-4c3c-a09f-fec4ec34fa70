//参考：https://blog.csdn.net/Gabriel_wei/article/details/90440542
{
  "workbench.activityBar.orientation": "vertical",
  "window.newWindowDimensions": "offset",
  //编辑设置
  "editor.tabSize": 2,

  "editor.detectIndentation": false, // 禁用自动检测缩进
  "[python]": {
    "editor.insertSpaces": true, // Python文件中使用空格
    "editor.tabSize": 4, // Python文件中的空格大小
    "editor.renderWhitespace": "all"
	}, // 设置缩放级别, 左侧目录栏字体大小
  "editor.renderWhitespace": "none",
  //右侧代码小地图显隐
  "editor.minimap.enabled": false,

	"editor.wordWrap": "off",
	"window.zoomLevel": 1.2,
  //编辑区字体大小
  "editor.fontSize": 14,
  "editor.insertSpaces": false,
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,
  "editor.scrollBeyondLastLine": false,
  "editor.find.addExtraSpaceOnTop": true,
  "editor.find.seedSearchStringFromSelection": false,
  "editor.selectionHighlight": false,
  "editor.trimAutoWhitespace": true,
  "editor.suggestSelection": "first",
  "editor.suggest.localityBonus": true,
  "editor.largeFileOptimizations": true,
  //是否显示悬停提示
  "editor.hover.enabled": true,
  //鼠标移动到悬停提示上时，是否保持可见
  "editor.hover.sticky": true,
  //禁止鼠标滚轮改变字体大小
  "editor.mouseWheelZoom": false,
  //按照 eslint 规则格式化并 fix
  "editor.codeActionsOnSave": {
    "source.fixAll.markdownlint": "explicit"
  },
  // "eslint.validate": ["javascript", "javascriptreact", "html", "vue"],
  "eslint.options": {
    "plugins": ["html"]
  },
  // rem to px
  // "cssrem.rootFontSize": 41.4,
  //guides 插件, 代码左侧的黄色竖线
  "guides.active.width": 1,
  "guides.active.color.dark": "yellow",
  "[markdown]": {
    "editor.quickSuggestions": {
      "comments": "on",
      "strings": "on",
      "other": "on"
    }
  },
  "search.exclude": {
    "**/node_modules": true,
    "library/": true
  },
  "python.pythonPath": "/usr/local/bin/python3",
  "emmet.syntaxProfiles": {
    "vue-html": "html",
    "vue": "html"
  },
  "emmet.includeLanguages": {
    "wxml": "html"
  },
  "emmet.preferences": {
    "css.webkitProperties": "border-right,animation",
    "css.mozProperties": "",
    "css.oProperties": null,
    "css.msProperties": null
  },
  "emmet.showSuggestionsAsSnippets": true,
  "emmet.showExpandedAbbreviation": "inMarkupAndStylesheetFilesOnly",
  // "debug.console.closeOnEnd": true,
  "editor.snippetSuggestions": "top",
  // "files.autoGuessEncoding": true,
  "files.encoding": "utf8",
  "files.autoSave": "off",
  // "files.autoSaveDelay":5000,
  "files.trimTrailingWhitespace": true,
  "files.associations": {
    "*.wpy": "vue",
    "*.cjson": "jsonc",
    "*.wxss": "css",
    "*.wxs": "javascript",
    "*.tmpl": "html",
    "*.json": "jsonc"
  },
  "files.eol": "\n",
  "files.exclude": {
    "**/.git": false,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    ".idea": true,
    ".vscode": false,
    "**/.DS_Store": true,
    "**/bower_components": true,
    "**/jspm_packages": true,
    "**/node_modules": false
    // 隐藏 node_modules 下 除了redux 之外的所有文件夹
    // "**/node_modules/{[^r],?[^e],??[^d],???[^u],????[^x]}*": true,
    //隐藏 node_modules 下以 a, b ,c 开头的文件夹
    // "**/node_modules/[abc]*": true
    // 匹配 除了abc 开头的其它词
    // "**/node_modules/[^abc]*": true
  },
  // "path-intellisense.mappings": {
  //   "config": "${workspaceRoot}/src/config",
  //   "component": "${workspaceRoot}/src/components",
  //   "utils": "${workspaceRoot}/src/utils",
  //   "@services": "${workspaceRoot}/src/services"
  // },
  "workbench.iconTheme": "vscode-great-icons",
  "workbench.startupEditor": "newUntitledFile",
  "workbench.editor.enablePreview": false,
  // "workbench.colorTheme": "Dracula",
  "workbench.colorCustomizations": {
    "gitlens.trailingLineForegroundColor": "#00ffa6af",
    "gitlens.trailingLineBackgroundColor": "#838382dd",
    "bookmarks.lineBorder": "#007dff",
    "editorCursor.foreground": "#ffd900c2",
    "editor.selectionBackground": "#2d2dc4",
    "editorLineNumber.activeForeground": "#12c678",
    "editor.lineHighlightBackground": "#1073cf2d",
    "editor.findMatchBackground": "#2db3ff",
    "peekViewResult.selectionBackground": "#3b92c469",
    "peekViewResult.background": "#2c4253",
    "peekViewEditor.matchHighlightBackground": "#ffd9007e",
    "peekViewEditorGutter.background": "#544545bf",
    "titleBar.activeForeground": "#ffd900c2",
    "tab.activeBorder": "#ffd900c2",
    "tab.activeBorderTop": "#ffd900c2",
    "tab.activeBackground": "#3b92c469",
    "list.activeSelectionForeground": "#fff",
    "list.activeSelectionBackground": "#1ad574d4",
    "list.inactiveSelectionBackground": "#1ad574d4",
    "list.inactiveSelectionForeground": "#fff",
    "list.hoverBackground": "#3b92c469",
    "editor.lineHighlightBorder": "#9fced11f"
  },
  //字符串颜色
  "editor.tokenColorCustomizations": {
    // "strings": "#FF0000"
  },
  "editor.renderLineHighlight": "all",
  "explorer.confirmDragAndDrop": false,
  "explorer.confirmDelete": true,
  "explorer.autoReveal": true,
  "javascript.suggestionActions.enabled": false,
  "html.format.wrapAttributes": "auto",
  //auto-header 自定义配置
  // "autoHeader": {
  //   "format": {
  //     "startWith": "/**",
  //     "middleWith": "*",
  //     "endWith": "*/",
  //     "headerPrefix": "@"
  //   },
  //   "header": {
  //     "Author": "Bruce",
  //     "Create Time": {
  //       "type": "createTime",
  //       "format": "YYYY-MM-DD HH:mm:ss"
  //     },
  //     "Modified by": {
  //       "type": "modifier",
  //       "value": "Bruce"
  //     },
  //     "Modified time": {
  //       "type": "modifyTime",
  //       "format": "YYYY-MM-DD HH:mm:ss"
  //     },
  //     "@": "-----------------------------------",
  //     "Description": " ",
  //     "Notice": " "
  //   }
  // },
  //Highlight Line插件配置 当前行下划线
  "highlightLine.borderStyle": "dashed",
  "highlightLine.borderWidth": "1px",
  "highlightLine.borderColor": "#FFD700",
  //标签高亮, 跳转
  "highlight-matching-tag.styles": {
    "closing": {
      // "name": {
      //   "underline": "yellow"
      // },
      //后标签<闭合标签>
      "full": {
        // "highlight": "#3cabdb",
        "surround": "#FFD700"
      }
    },
    "opening": {
      //前标签
      // "name": {
      //   "highlight": "red",
      //   "underline": "yellow"
      // },
      "full": {
        "surround": "#FFD700"
      }
      // "left": {
      //   "highlight": "#989595",
      // },
      // "right": {
      //   "highlight": "#5D5D5D",
      // }
    }
  },
  //终端设置
  "terminal.integrated.windowsEnableConpty": true,
  "terminal.integrated.shell.osx": "/bin/zsh",
  "explorer.enableDragAndDrop": false,
  "explorer.incrementalNaming": "smart",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  /**
    vim-sneak
     快速跳转 : s/S <char><char> 转到下/上一个char char出现的地方: 连续输入两个字母
  */
  "vim.sneak": true,
  //vim-surround
  /**
    d s <existing> 删除两边的符号: 例如 "name"  ds" 即 删除两边的 "
    c s <existing> <desired> 替换两边的符号: cs"'  将 " 替换为 '
  */
  "vim.surround": true,
  // "vim.handleKeys": {
  //   "<A-o>": false,
  // },
  //以驼峰跳转单词
  "vim.camelCaseMotion.enable": true,
  //vim-easymotion 极速跳转
  "vim.easymotion": true,
  "vim.easymotionKeys": "asdfghjklwenmio",
  "vim.easymotionMarkerBackgroundColor": "#000",
  "vim.easymotionMarkerForegroundColorOneChar": "#e6eba0",
  "vim.easymotionMarkerForegroundColorTwoCharFirst": "#E9F01D",
  "vim.easymotionMarkerForegroundColorTwoCharSecond": "#aeb108",
  "vim.easymotionMarkerHeight": 22,
  "vim.easymotionMarkerFontWeight": "bold",
  "vim.easymotionMarkerYOffset": 10,
  "vim.easymotionMarkerWidthPerChar": 15,
  "vim.easymotionMarkerFontFamily": "unset",
  //vim插件
  "vim.highlightedyank.enable": true,
  "vim.highlightedyank.duration": 1000,
  //使用系统粘贴板
  "vim.useSystemClipboard": true,
  // "vim.useCtrlKeys": true,
  "vim.searchHighlightColor": "#2db3ff",
  "vim.searchHighlightTextColor": "black",
  "vim.leader": "<space>",
  "vim.handleKeys": {
    "<C-j>": true
  },
  // 插入模式下
  "vim.insertModeKeyBindings": [
    {
      "before": ["j", "j"],
      "after": ["<Esc>"]
    },
    // ctrl+j: 向下移动5行
    {
      "before": ["<C-j>"],
      "commands": ["cursorDown", "cursorDown", "cursorDown"]
    },
    {
      "before": ["<C-k>"],
      "commands": ["cursorUp", "cursorUp", "cursorUp"]
    },
    {
      "before": ["<C-l>"],
      "commands": [
        "cursorRight",
        "cursorRight",
        "cursorRight",
        "cursorRight",
        "cursorRight"
      ]
    },
    {
      "before": ["<C-h>"],
      "commands": [
        "cursorLeft",
        "cursorLeft",
        "cursorLeft",
        "cursorLeft",
        "cursorLeft"
      ]
    }
  ],
  //可视模式下
  "vim.visualModeKeyBindingsNonRecursive": [
    {
      "before": ["d"],
      "after": ["\"", "_", "d"]
    },
    {
      "before": ["D"],
      "after": ["\"", "_", "D"]
    },
    {
      "before": ["s"],
      "after": ["\"", "_", "s"]
    },
    {
      "before": ["<C-j>"],
      "after": ["3", "j"]
    },
    {
      "before": ["<C-k>"],
      "after": ["3", "k"]
    },
    {
      "before": ["<C-l>"],
      "after": ["5", "l"]
    },
    {
      "before": ["<C-h>"],
      "after": ["5", "h"]
    }
  ],
  // 正常模式下
  "vim.normalModeKeyBindingsNonRecursive": [
    {
      "before": ["<leader>", "v", "{"],
      "after": ["0", "<Shift>", "v", "/", "{", "<CR>", "%"]
    },
    {
      "before": ["\\"],
      "commands": ["editor.action.jumpToBracket"]
    },
    /**
     *  css navigation 插件, 从 css 跳转到  对应的html tag
     *  用法: 在 css 样式中按 gr 选中标签, 然后按 enter, esc
     */
    {
      "before": ["g", "r"],
      "commands": ["editor.action.referenceSearch.trigger"]
    },
    {
      "before": ["[", "["],
      "after": ["[", "{"]
    },
    {
      "before": ["]", "]"],
      "after": ["]", "}"]
    },
    {
      "before": ["m", "m"],
      "after": ["z", "z"]
    },
    {
      "before": ["t", "t"],
      "after": ["z", "t"]
    },
    {
      "before": ["<C-j>"],
      "after": ["3", "j"]
    },
    {
      "before": ["<C-k>"],
      "after": ["3", "k"]
    },
    {
      "before": ["<C-l>"],
      "after": ["5", "l"]
    },
    {
      "before": ["<C-h>"],
      "after": ["5", "h"]
    },
    // {
    //   "before": ["K"],
    //   "after": ["g", "t"]
    // },
    // {
    //   "before": ["J"],
    //   "after": ["g", "T"]
    // },
    {
      "before": ["-"],
      "commands": ["workbench.action.navigateBack"]
    },
    {
      "before": ["="],
      "commands": ["workbench.action.navigateForward"]
    },
    {
      "before": ["X"],
      "commands": ["workbench.action.reopenClosedEditor"]
    },
    {
      "before": ["x"],
      "commands": ["workbench.action.closeActiveEditor"]
    },
    {
      "before": [","],
      "commands": ["highlight-matching-tag.jumpToMatchingTag"]
    },
    // 向下翻半页
    // {
    //   "before": ["0"],
    //   "after": ["<C-d>"]
    // },
    // {
    //   "before": ["2"],
    //   "after": ["<C-d>"]
    // },
    // 向上翻半页
    // {
    //   "before": ["9"],
    //   "after": ["<C-u>"]
    // },
    // {
    //   "before": ["1"],
    //   "after": ["<C-u>"]
    // },
    {
      "before": ["n"],
      "after": ["*"]
    },
    {
      "before": ["N"],
      "after": ["#"]
    },
    {
      "before": ["d"],
      "after": ["\"", "_", "d"]
    },
    {
      "before": ["D"],
      "after": ["\"", "_", "D"]
    },
    // {
    //   "before": ["x"],
    //   "after": ["\"", "_", "x"]
    // },
    {
      "before": ["s"],
      "after": ["\"", "_", "s"]
    },
    // d 代替 di
    {
      "before": ["d", "w"],
      "after": ["\"", "_", "d", "i", "w"]
    },
    {
      "before": ["d", "["],
      "after": ["\"", "_", "d", "i", "["]
    },
    {
      "before": ["d", "{"],
      "after": ["\"", "_", "d", "i", "{"]
    },
    {
      "before": ["d", "("],
      "after": ["\"", "_", "d", "i", "("]
    },
    {
      "before": ["d", "\""],
      "after": ["\"", "_", "d", "i", "\""]
    },
    {
      "before": ["d", "'"],
      "after": ["\"", "_", "d", "i", "'"]
    },
    {
      "before": ["d", "t"],
      "after": ["\"", "_", "d", "i", "t"]
    },
    // y 代替 yi
    {
      "before": ["y", "w"],
      "after": ["y", "i", "w"]
    },
    {
      "before": ["y", "["],
      "after": ["y", "i", "["]
    },
    {
      "before": ["y", "{"],
      "after": ["y", "i", "{"]
    },
    {
      "before": ["y", "("],
      "after": ["y", "i", "("]
    },
    {
      "before": ["y", "\""],
      "after": ["y", "i", "\""]
    },
    {
      "before": ["y", "'"],
      "after": ["y", "i", "'"]
    },
    {
      "before": ["y", "t"],
      "after": ["y", "i", "t"]
    },
    // c 代替 ci
    {
      "before": ["c", "w"],
      "after": ["c", "i", "w"]
    },
    {
      "before": ["c", "["],
      "after": ["c", "i", "["]
    },
    {
      "before": ["c", "{"],
      "after": ["c", "i", "{"]
    },
    {
      "before": ["c", "("],
      "after": ["c", "i", "("]
    },
    {
      "before": ["c", "\""],
      "after": ["c", "i", "\""]
    },
    {
      "before": ["c", "'"],
      "after": ["c", "i", "'"]
    },
    {
      "before": ["c", "t"],
      "after": ["c", "i", "t"]
    },
    // v 代替 vi
    {
      "before": ["v", "w"],
      "after": ["v", "i", "w"]
    },
    {
      "before": ["v", "["],
      "after": ["v", "i", "["]
    },
    {
      "before": ["v", "{"],
      "after": ["v", "i", "{"]
    },
    {
      "before": ["v", "("],
      "after": ["v", "i", "("]
    },
    {
      "before": ["v", "\""],
      "after": ["v", "i", "\""]
    },
    {
      "before": ["v", "'"],
      "after": ["v", "i", "'"]
    },
    {
      "before": ["v", "t"],
      "after": ["v", "i", "t"]
    },
    {
      "before": ["<leader>", "o"],
      "commands": ["breadcrumbs.focusAndSelect"]
    },
    {
      "before": ["W"],
      "after": ["<leader>", "w"]
    },
    {
      "before": ["B"],
      "after": ["<leader>", "b"]
    },
    {
      "before": ["f"],
      "after": ["<leader>", "<leader>", "<leader>", "b", "d", "w"]
    },
    {
      "before": ["<leader>", "j"],
      "after": ["<leader>", "<leader>", "<leader>", "b", "d", "j", "k"]
    }
  ],
  "javascript.updateImportsOnFileMove.enabled": "always",
  //vue文件中的html 格式化按照 beautify
  "vetur.format.defaultFormatter.html": "js-beautify-html",
  //设置只跳转相应的符号
  "gotoNextPreviousMember.symbolKinds": [
    "function",
    "method",
    "constructor",
    "object",
    "class",
    "field",
    "event"
  ],
  "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue",
  //任何地方都可以打断点
  "debug.allowBreakpointsEverywhere": true,
  //默认翻译为中文简体
  "commentTranslate.targetLanguage": "zh-CN",
  "liveServer.settings.donotShowInfoMsg": true,
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // vue-helper
  "vue-helper.alias": {
    "@": "."
  },
  "vue-helper.rem-px": 75,
  "minapp-vscode.disableAutoConfig": true,
	"px-to-rem.number-of-decimals-digits": 4,
	"px-to-rem.px-per-rem": 16,
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "HookyQR.beautify"
  },
  // Easy Less
  "less.compile": {
    "out": true,
    "outExt": ".css"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 切换书签时，只在当前文件之间的书签切换
  "bookmarks.navigateThroughAllFiles": false,
  "editor.cursorStyle": "line",
  "editor.lineNumbers": "on",
  // 单词分隔符, 字符之间有以下字符的则视为多个单词
  "editor.wordSeparators": "/\\()\"':,.;<>~!@#$%^&*|+=[]{}`?-",
  "security.workspace.trust.untrustedFiles": "open",
  "editor.cursorSmoothCaretAnimation": "on",
  "selectby.regexes": {
    // 主要针对vue中的三个部分快速跳转
        "goToTemplate": {
						"moveby": "<template"
        },
        "goToStyle": {
            "moveby": "<style"
        },
        "goToScript": {
            "moveby": "<script"
        },
        "goToRender": {
            "moveby": "render()"
        }
  },
  // gitlens 插件
  "gitlens.currentLine.enabled": true,
  "gitlens.blame.heatmap.location": "right",
  "gitlens.blame.highlight.enabled": true,
  // 鼠标所在行 显示内容格式
  "gitlens.currentLine.format": "${author}, ${date}${' via 'pullRequest}${ • message|50?}",
  "gitlens.currentLine.dateFormat": "MM-DD",
  "gitlens.hovers.annotations.enabled": false,
  // 指定默认情况下绝对日期的格式
  "gitlens.defaultDateFormat": "YYYY-MM-DD HH:mm",
  // 指定是否在悬停中显示化身图像
  "gitlens.hovers.avatars": false,
  //指定是否为当前行提供提交详细信息悬停
  "gitlens.hovers.currentLine.details": true,
  "gitlens.hovers.detailsMarkdownFormat": "${avatar} &nbsp;__${author}__, ${ago}${' via 'pullRequest} &nbsp; _(${date})_ \n\n${message}\n\n${commands}${\n\n---\n\nfootnotes}",
  "[bat]": {},
  "workbench.editor.scrollToSwitchTabs": true,
  "editor.guides.indentation": false,
  "editor.inlineSuggest.enabled": true,
  "editor.accessibilitySupport": "off",
  "tabnine.experimentalAutoImports": true,
  "Notes.notesLocation": "/Users/<USER>/Desktop/前端笔记",
  "blockman.n01LineHeight": 0,
  "diffEditor.wordWrap": "off",
  "editor.guides.bracketPairs": false,
  "editor.inlayHints.enabled": false,
  "git.ignoreMissingGitWarning": true,
  "liveServer.settings.ChromeDebuggingAttachment": false,
  "editor.formatOnSave": true,
  "explorer.autoRevealExclude": {
    "**/node_modules": false
  },
  "editor.inlineSuggest.suppressSuggestions": true,
  "extensions.autoUpdate": false,
  "github.copilot.chat.localeOverride": "zh-CN",
  "editor.inlineSuggest.showToolbar": "always",
  "path-intellisense.extensionOnImport": true,

  // koroFileHeader
  // 插件配置选项
  "fileheader.configObj": {
    "createFileTime": true, // 当前时间/创建文件时间, 设为false更改为当前生成注释的时间
    "language": {
      "languagetest": {
        "head": "/$$",
        "middle": " $ @",
        "end": " $/"
      }
    },
    "autoAdd": true,
    "autoAddLine": 100,
    "autoAlready": true,
    "annotationStr": {
      "head": "/*",
      "middle": " * @",
      "end": " */",
      "use": false
    },
    "headInsertLine": {
      "php": 2,
      "sh": 2
    },
    "beforeAnnotation": {
      "文件后缀": "该文件后缀的头部注释之前添加某些内容"
    },
    "afterAnnotation": {
      "文件后缀": "该文件后缀的头部注释之后添加某些内容"
    },
    "specialOptions": {
      "特殊字段": "自定义比如LastEditTime/LastEditors"
    },
    "switch": {
      "newlineAddAnnotation": true
    },
    "supportAutoLanguage": [],
    "prohibitAutoAdd": ["json"],
    "folderBlacklist": ["node_modules", "文件夹禁止自动添加头部注释"],
    "prohibitItemAutoAdd": [
      "项目的全称, 整个项目禁止自动添加头部注释, 可以使用快捷键添加"
    ],
    "moveCursor": true,
    "dateFormat": "YYYY-MM-DD HH:mm:ss", // moment.js的format方法的参数
    "atSymbol": ["@", "@"],
    "atSymbolObj": {
      "文件后缀": ["头部注释@符号", "函数注释@符号"]
    },
    "colon": [": ", ": "],
    "colonObj": {
      "文件后缀": ["头部注释冒号", "函数注释冒号"]
    },
    "filePathColon": "路径分隔符替换",
    "showErrorMessage": false,
    "writeLog": false,
    "wideSame": true, // // 设置为true开启等宽设置
    "wideNum": 13, // 头部注释等宽设置wideSame
    "functionWideNum": 0, // 函数注释等宽设置:0 默认关闭 设置一个正整数即可开启 比如12
    "CheckFileChange": false,
    "createHeader": true, // 新建文件自动添加头部注释
    "useWorker": false,
    "designAddHead": false,
    "headDesignName": "random",
    "headDesign": false,
    "cursorModeInternalAll": {},
    "openFunctionParamsCheck": true, // 函数注释自动提取函数的参数
    "functionParamsShape": ["{", "}"],
    "functionBlankSpaceAll": {},
    "functionTypeSymbol": "*",
    "typeParamOrder": "type param", // 参数类型 和 参数的位置自定义
    "customHasHeadEnd": {},
    "throttleTime": 60000
  },
  // 函数注释: 在光标处插入
  "fileheader.cursorMode": {
    // "func": "",
    "description": "",
    "param": "Do not edit",
    "return": "Do not edit"
    // "example": "",
  },
  // 头部注释
  "fileheader.customMade": {
    "Author": "Bruce",
    "Date": "Do not edit",
    "LastEditors": "Bruce",
    "LastEditTime": "Do not edit",
    "FilePath": "no item name", // 去掉项目名称,  only file name可以去掉路径，只展示文件名
    "Description": "",
    "custom_string_obkoro1_copyright": "Copyright ${now_year} Bruce, All Rights Reserved. ", // 版权声明 保留所有权利 自动替换年份
    "custom_string_obkoro1_date": "Do not edit" // 版权时间
  },
  "github.copilot.enable": {
		"*": false
	},
  "extensions.ignoreRecommendations": true,
  "liveServer.settings.donotVerifyTags": true,
  "Lingma.DisplayLanguage": "简体中文",
  "settingsSync.ignoredSettings": [],
  "settingsSync.ignoredExtensions": [],
  "accessibility.voice.speechLanguage": "zh-CN",
  "roo-cline.allowedCommands": [
		"npm test",
		"npm install",
		"tsc",
		"git log",
		"git diff",
		"git show",
		"cat,ls"
	],

  "terminal.integrated.env.osx": {
    "PATH": "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:${env:PATH}"
  },
  "terminal.integrated.defaultProfile.osx": "zsh",
  "augment.chat.userGuidelines": "所有问题使用中文回答",
  "redhat.telemetry.enabled": true,
  "todo-tree.general.tags": [
    "BUG",
    "HACK",
    "FIXME",
    "TODO",
    "XXX",
    "[ ]",
    "[x]"
  ],
	"todohighlight.isEnable": false,
	"codeflip.config": {
		"targetLanguage": "cpp",
		"geminiApiKey": "AIzaSyDmzuuTM2-V1EBUAbFxvAp2ngTxlLf_p2Y",
		"currentModelId": "gemini",
		"interfaceLanguage": "zh-cn"
	},
	"roo-cline.deniedCommands": [],
	"diffEditor.ignoreTrimWhitespace": false,


  // koro1FileHeader end
  // ……
}