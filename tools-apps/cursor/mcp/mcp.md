<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-02-16 23:56:19
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-02-22 18:34:23
 * @FilePath     : /tools-apps/cursor/mcp/mcp.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-02-16 23:56:19
-->

- [modelcontextprotocol](https://github.com/modelcontextprotocol/servers)
- [modelcontextprotocol](https://modelcontextprotocol.io/introduction)
- [cursor.directory](https://cursor.directory/mcp)
- [mcp.so](https://mcp.so/)
- [supabase](https://docs-git-docs-mcp-supabase.vercel.app/docs/guides/getting-started/mcp)

<!-- 已安装 -->

- [browser-tools](https://browsertools.agentdesk.ai/installation)
