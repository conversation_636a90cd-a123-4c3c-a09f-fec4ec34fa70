# Xcode 生成的文件
build/
DerivedData/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
*.xcuserstate
*.xcscmblueprint

# CocoaPods 依赖管理文件
Pods/
Podfile.lock

# Carthage 依赖管理文件
Carthage/Build
Carthage/Checkouts

# Swift Package Manager 相关文件
.build/
Package.resolved

# 测试相关文件
*.gcda
*.gcno
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/
fastlane/test_output

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器配置文件
.idea/
.vscode/