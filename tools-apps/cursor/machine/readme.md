<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2024-12-05 11:36:22
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-02-20 15:37:59
 * @FilePath     : /tools-apps/cursor/machine/readme.md
 * @Description  : Cursor Machine ID 修改指南
 * Copyright 2024 Bruce, All Rights Reserved.
 * 2024-12-05 11:36:22
-->

# Cursor Machine ID 修改指南

## 问题背景

当 Cursor 多次删除账户重新登录后，会提示 "Too many free trial accounts used on this machine."，导致无法继续使用 Claude-3.5-sonnet。这时需要修改 machine ID 来继续使用。

## 最新解决方案（推荐）

### 方案一：Cursor Shadow Patch

使用 [cursor-shadow-patch](https://github.com/zetaloop/cursor-shadow-patch) 工具，这是目前最新且稳定的解决方案。

### 方案二：Go Cursor Help

参考 [go-cursor-help](https://github.com/yuaotian/go-cursor-help/issues/191) 提供的解决方案。

## 传统解决方案

以下是一些早期的解决方法，仅供参考：

### 方法一：直接删除 machineID 文件

文件位置：

- Windows: `~\AppData\Roaming\Cursor`
- macOS: `~/Library/Application Support/cursor`

### 方法二：安装插件

1. 从 GitHub 下载插件：[cursor-fake-machine-0.0.1.vsix](https://github.com/bestK/cursor-fake-machine/releases/download/v0.0.1/cursor-fake-machine-0.0.1.vsix)
2. 打开 Cursor
3. 将下载的 .vsix 文件拖入 Cursor 界面的左侧区域完成安装

### 方法三：使用 Python 脚本

1. 切换到 `changeCursorMachineID.py` 所在目录
2. 运行以下命令：
   - 查看当前机器码：`python3 changeCursorMachineID.py ids`
   - 生成随机机器码：`python3 changeCursorMachineID.py random-ids`

> 注：在 Windows 11 上已测试 Python 脚本方法有效。

## 其他参考

更多解决方案和讨论：

- [cursor-shadow-patch](https://github.com/zetaloop/cursor-shadow-patch) - 最新的补丁工具
cd ~/Downloads/cursor-shadow-patch-main
python3 patcher.py
- [go-cursor-help #191](https://github.com/yuaotian/go-cursor-help/issues/191) - 社区讨论和解决方案
- [cursor-auto-free](https://cursor-auto-free-doc.vercel.app/zh/quick-start.html)
