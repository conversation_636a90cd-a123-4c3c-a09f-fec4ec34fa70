<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-16 10:00:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-16 13:44:40
 * @FilePath     : /tools-apps/cursor/rules/web-dev.md
 * @Description  : Web前端开发特有规则指南
 * Copyright 2025 Bruce, All Rights Reserved.
-->

# Web前端开发特有规范

## HTML规范

1. **语义化标签**：
   - 使用恰当的HTML5语义化标签（如`<header>`, `<footer>`, `<nav>`, `<article>`, `<section>`等）
   - 避免过度使用无语义的`<div>`和`<span>`标签
   - 使用`<button>`而非`<div class="button">`来创建按钮

2. **属性规范**：
   - 使用双引号包裹属性值
   - 布尔属性（如`disabled`, `checked`）不需要指定值
   - 自定义数据属性使用`data-*`前缀

3. **表单设计**：
   - 为表单控件使用适当的`<label>`标签并通过`for`属性关联
   - 使用适当的表单控件类型（如`email`, `tel`, `date`等）
   - 提供适当的表单验证属性（如`required`, `pattern`, `min`, `max`等）

4. **可访问性**：
   - 为图片提供有意义的`alt`属性
   - 使用适当的ARIA属性增强可访问性
   - 确保页面结构有清晰的标题层次（h1-h6）

## CSS规范

1. **选择器规范**：
   - 避免使用过于通用的选择器（如`*`, `div`, `span`等）
   - 避免过深的选择器嵌套（不超过3层）
   - 使用类选择器而非ID选择器（除非特定需求）
   - 避免使用`!important`（除非必要）

2. **命名规范**：
   - 使用有意义的类名，反映元素的用途而非外观
   - 采用一致的命名方式（如BEM, OOCSS, SMACSS等）
   - 避免使用简写或缩写类名（除非是约定俗成的）

   ```css
   /* BEM命名示例 */
   .card {} /* 块 */
   .card__title {} /* 元素 */
   .card--featured {} /* 修饰符 */
   ```

3. **属性书写顺序**：
   - 按照一定的顺序书写CSS属性，如：
     - 定位属性（position, top, right, z-index等）
     - 盒模型属性（display, box-sizing, width, margin等）
     - 文字排版（font, line-height, text-align等）
     - 视觉效果（color, background, border, opacity等）
     - 其他（cursor, overflow, transition等）

4. **响应式设计**：
   - 采用移动优先（Mobile First）的设计方法

5. **性能优化**：
   - 避免使用CSS表达式
   - 避免使用@import
   - 使用简写属性（如`padding: 10px 20px`而非分别设置四个方向）
   - 避免重复定义属性

## JavaScript规范

1. **DOM操作**：
   - 最小化DOM操作，避免频繁操作DOM
   - 使用文档片段（DocumentFragment）一次性添加多个元素
   - 缓存频繁使用的DOM元素引用
   - 使用事件委托处理多个元素的相同事件

2. **事件处理**：
   - 使用addEventListener而非on*属性
   - 移除不再需要的事件监听器
   - 避免匿名函数作为事件处理器（便于后续移除）
   - 防止事件处理器中的this指向问题

3. **异步编程**：
   - 使用Promise或async/await处理异步操作
   - 避免回调地狱（Callback Hell）
   - 适当处理异步操作的错误和加载状态
   - 使用防抖（Debounce）和节流（Throttle）优化频繁触发的事件

4. **前端路由**：
   - 使用有意义的URL路径
   - 确保路由可以被书签收藏
   - 处理路由变化时的页面状态保持
   - 提供适当的加载和错误状态

## 前端安全

1. **XSS防护**：
   - 对用户输入进行转义或净化
   - 使用textContent而非innerHTML插入文本内容
   - 使用CSP（Content Security Policy）限制资源加载
   - 避免eval()和new Function()等动态执行代码的方法

2. **CSRF防护**：
   - 在表单中使用CSRF令牌
   - 验证请求来源（Origin和Referer头）
   - 对敏感操作使用二次确认

3. **数据存储**：
   - 不在本地存储敏感信息
   - 了解localStorage, sessionStorage和Cookie的区别和适用场景
   - 为Cookie设置适当的属性（HttpOnly, Secure, SameSite等）

## 前端性能优化

1. **资源加载**：
   - 使用适当的图片格式和大小
   - 实现懒加载（图片、组件等）
   - 合理使用预加载、预连接和预获取
   - 减少HTTP请求数量（合并文件、CSS Sprites等）

2. **渲染性能**：
   - 避免强制同步布局（Layout Thrashing）
   - 使用CSS3硬件加速（transform, opacity等）
   - 优化动画性能（使用requestAnimationFrame等）
   - 减少重排（reflow）和重绘（repaint）

3. **JavaScript性能**：
   - 避免长时间运行的JavaScript阻塞主线程
   - 使用Web Workers处理耗时计算
   - 使用高效的数据结构和算法
   - 避免内存泄漏（如未移除的事件监听器）

## 用户体验优化

1. **加载体验**：
   - 提供适当的加载指示器
   - 实现骨架屏（Skeleton Screen）
   - 优先加载关键内容
   - 避免内容跳动（Content Jump）

2. **交互反馈**：
   - 提供即时的操作反馈
   - 使用适当的动画和过渡效果
   - 确保可点击元素有足够的点击区域
   - 提供适当的表单验证反馈

3. **错误处理**：
   - 提供友好的错误信息
   - 引导用户如何解决问题
   - 在适当的位置显示错误信息
   - 保留用户已输入的数据

## 跨浏览器兼容性

1. **特性检测**：
   - 使用特性检测而非浏览器检测
   - 为不支持的特性提供优雅降级
   - 使用polyfill支持旧浏览器

2. **CSS兼容性**：
   - 了解不同浏览器的CSS差异
   - 适当使用浏览器前缀（推荐通过自动化工具添加）
   - 测试关键布局在不同浏览器中的表现

3. **移动设备兼容**：
   - 处理触摸事件和手势
   - 考虑不同设备的性能差异
   - 测试不同屏幕尺寸的显示效果
