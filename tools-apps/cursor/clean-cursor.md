<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2024-10-30 11:01:56
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2024-10-30 11:01:56
 * @FilePath     : /tools-apps/cursor/clean-cursor.md
 * @Description  :
 * Copyright 2024 Bruce, All Rights Reserved.
 * 2024-10-30 11:01:56
-->
# macOS 彻底清除 Cursor 应用指南

macOS 中完全卸载应用需要清理所有相关文件，包括缓存、配置等。以下是完整的清理步骤：

## 1. 删除应用程序

1. 打开 Finder > **应用程序**
2. 将 Cursor 移至废纸篓并清空

## 2. 清理系统残留

在终端执行以下命令清理所有相关文件：

sudo rm -rf /Applications/Cursor.app ~/Library/Application\ Support/Cursor ~/Library/Preferences/com.cursor.*~/Library/Caches/com.cursor.* ~/Library/Logs/com.cursor.*~/Library/Saved\ Application\ State/com.cursor.* /Library/Application\ Support/Cursor /Library/Preferences/com.cursor.*/Library/Caches/com.cursor.*

setopt +o nomatch && sudo rm -rf /Applications/Cursor.app ~/Library/Application\ Support/Cursor ~/Library/Preferences/com.cursor.*~/Library/Caches/com.cursor.* ~/Library/Logs/com.cursor.*~/Library/Saved\ Application\ State/com.cursor.* /Library/Application\ Support/Cursor /Library/Preferences/com.cursor.*/Library/Caches/com.cursor.*

此命令会清理：

- 应用程序文件
- 用户配置和缓存
- 系统级配置和缓存

## 3. 检查残留文件

运行以下命令检查是否还有遗漏：

sudo find /Applications /Library ~/Library -name "*Cursor*" -exec rm -rf {} +

## 4. 完成清理

重启电脑以确保所有更改生效。
