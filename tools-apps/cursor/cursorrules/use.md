
<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2024-11-21 14:39:45
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2024-11-21 14:48:30
 * @FilePath     : /tools-apps/cursor/cursorrules/.cursor/requirements/use.md
 * @Description  :
 * Copyright 2024 Bruce, All Rights Reserved.
 * 2024-11-21 14:39:45
-->
需求分析:

 功能优先级:

 技术方案:

1.架构设计
2.基础框架搭建
3.功能开发

项目简述：一句话描述项目（例如："开发一个记账APP"）
技术栈：

- 语言：Kotlin
- 架构：MVVM
- 主要框架：Jetpack Components
- UI：XML/Compose

第一轮：需求分析
"基于[项目简述]，请提供：

1. 市场分析及竞品功能对比
2. 核心用户需求分析
3. 潜在风险点分析"

第二轮：功能优先级
"基于上述分析，请：

1. 列出所有必要功能
2. 按照优先级分级（P0/P1/P2）
3. 给出功能依赖关系
4. 预估开发难度"

第三轮：技术方案
"基于[技术栈]和功能列表，请提供：

1. 详细的架构设计
2. 技术选型理由
3. 关键技术点解决方案
4. 性能优化建议"
