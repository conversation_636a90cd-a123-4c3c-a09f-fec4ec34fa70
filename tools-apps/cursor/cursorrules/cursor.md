<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2024-11-22 10:35:10
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-02-12 14:57:57
 * @FilePath     : /tools-apps/cursor/cursorrules/cursor.md
 * @Description  :
 * Copyright 2024 Bruce, All Rights Reserved.
 * 2024-11-22 10:35:10
-->

# Cursor 各种设置

## .cursorrules 文件红色波浪线解决方法

当你看到 .cursorrules 文件出现红色波浪线时，请按照以下步骤操作：

1. 按下 `Cmd/Ctrl + Shift + P` 打开命令面板
2. 在命令面板中输入 "Change Language Mode"
3. 从下拉列表中选择 "JSON with Comments"

这样操作后，红色波浪线应该就会消失。

## 每次新开项目的时候都加上这一句

"在这个项目中，我们将遵循 .cursorrules 中定义的规范进行开发。请在所有代码生成和修改过程中，自动应用这些规则，不需要我每次都提醒你。"

<https://cursor101.com/zh>
