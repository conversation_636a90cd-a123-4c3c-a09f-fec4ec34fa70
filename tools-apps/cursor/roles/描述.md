<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-12 07:26:27
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-12 08:06:03
 * @FilePath     : /tools-apps/cursor/roles/描述.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-12 07:26:27
-->

我想要写一个比较好的 prompt, 它的主要功能如下: 1.根据我提出的需求，提供的文件，描述的信息进行分析，推理业务逻辑，要基于交互设计，编码专家的多重角度来分析 2.分析完成以后，要继续画图，比如流程图，数据流图，状态图，等等，这是为了方便编程领域人员的快速准确的理解功能，以此进行准确无误的编码

- 精简重复内容
  - 增强示例实用性
  - 优化图表选择决策树
  - 增加快速启动模式
  - 标准化输出模板

我正在开发一个移动端 app, 现在有一个编辑登录用户信息的功能，请你根据 帮我分析这个功能，要注意编辑信息成功以后，项目中所有使用用户信息的页面在调用的时候都要是最新信息
