user www www;

worker_processes auto;
worker_cpu_affinity auto;

error_log /home/<USER>/nginx_error.log crit;

pid /usr/local/nginx/logs/nginx.pid;

#Specifies the value for maximum file descriptors that can be opened by this process.
worker_rlimit_nofile 51200;

events {
	use epoll;
	worker_connections 51200;
	multi_accept off;
	accept_mutex off;
}

http {
	include mime.types;
	default_type application/octet-stream;

	server_names_hash_bucket_size 128;
	client_header_buffer_size 32k;
	large_client_header_buffers 4 32k;
	client_max_body_size 50m;

	sendfile on;
	sendfile_max_chunk 512k;
	tcp_nopush on;

	keepalive_timeout 60;

	tcp_nodelay on;

	fastcgi_connect_timeout 300;
	fastcgi_send_timeout 300;
	fastcgi_read_timeout 300;
	fastcgi_buffer_size 64k;
	fastcgi_buffers 4 64k;
	fastcgi_busy_buffers_size 128k;
	fastcgi_temp_file_write_size 256k;

	gzip on;
	gzip_min_length 1k;
	gzip_buffers 4 16k;
	gzip_http_version 1.1;
	gzip_comp_level 2;
	gzip_types text/plain application/javascript application/x-javascript text/javascript text/css application/xml application/xml+rss;
	gzip_vary on;
	gzip_proxied expired no-cache no-store private auth;
	gzip_disable "MSIE [1-6]\.";

	#limit_conn_zone $binary_remote_addr zone=perip:10m;
	##If enable limit_conn_zone,add "limit_conn perip 10;" to server section.

	server_tokens off;
	access_log off;

	# 新增：IP直接访问配置 - 允许通过IP直接访问网站
	server {
		listen 80;
		listen [::]:80;
		server_name **********;

		# 根目录设置
		root /home/<USER>/default;
		index index.php index.html index.htm;

		# PHP解析配置
		location ~ \.php$ {
			try_files $uri =404;
			fastcgi_pass unix:/tmp/php-cgi.sock;
			fastcgi_index index.php;
			fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
			include fastcgi_params;
		}

		# 默认location
		location / {
			try_files $uri $uri/ /index.php?$args;
		}

		# 静态文件缓存
		location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
			expires 30d;
		}

		location ~ .*\.(js|css)?$ {
			expires 12h;
		}

		location ~ /.well-known {
			allow all;
		}

		location ~ /\. {
			deny all;
		}
	}

	# HTTP 80端口配置：域名访问支持（临时禁用HTTPS强制跳转）
	server {
		listen 80;
		listen [::]:80;
		#listen 0.0.0.0:80;
		server_name www.lineagew.kr lineagew.kr;
		#server_name wp.talkatest.com;

		# 临时注释掉HTTPS强制跳转，等DNS解析正常后再启用
		# return 301 https://lineagew.kr$request_uri;

		# 根目录设置（确认路径是否正确！）
		root /home/<USER>/default;
		index index.php index.html index.htm;

		# PHP解析配置
		location ~ \.php$ {
			try_files $uri =404;
			fastcgi_pass unix:/tmp/php-cgi.sock;
			fastcgi_index index.php;
			fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
			include fastcgi_params;
		}

		# 默认location - WordPress规则
		location / {
			try_files $uri $uri/ /index.php?$args;
		}

		# 静态文件缓存
		location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
			expires 30d;
		}

		location ~ .*\.(js|css)?$ {
			expires 12h;
		}

		location ~ /.well-known {
			allow all;
		}

		location ~ /\. {
			deny all;
		}
	}

	# HTTPS 443端口配置
	server {
		listen 443 ssl;
		listen [::]:443 ssl;
		http2 on; # 独立启用HTTP/2
		server_name lineagew.kr;
		#server_name wp.talkatest.com;


		# SSL证书路径
		#ssl_certificate /usr/local/nginx/ssl/cert.pem;
		#ssl_certificate_key /usr/local/nginx/ssl/key.pem;
		ssl_certificate /usr/local/nginx/ssl/lineagew.kr/cert.pem;
		ssl_certificate_key /usr/local/nginx/ssl/lineagew.kr/key.pem;

		# SSL协议和加密套件
		ssl_protocols TLSv1.2 TLSv1.3;
		ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
		ssl_prefer_server_ciphers on;
		ssl_session_cache shared:SSL:10m;
		ssl_session_timeout 10m;

		# 根目录设置（确认路径是否正确！）
		root /home/<USER>/default;
		index index.php index.html index.htm;

		# 强制统一域名（避免重复内容）
		#if ($host != 'www.lineagew.kr') {
		#    return 301 https://www.lineagew.kr$request_uri;
		#}

		# PHP解析配置
		location ~ \.php$ {
			try_files $uri =404;
			fastcgi_pass unix:/tmp/php-cgi.sock; # 根据实际PHP-FPM配置调整
			fastcgi_index index.php;
			fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
			include fastcgi_params;
		}

		# 静态文件缓存和WordPress规则
		location / {
			try_files $uri $uri/ /index.php?$args;
		}

		# 其他安全配置（可选）
		location ~ /\.ht {
			deny all;
		}
	}

	include vhost/*.conf;
}
