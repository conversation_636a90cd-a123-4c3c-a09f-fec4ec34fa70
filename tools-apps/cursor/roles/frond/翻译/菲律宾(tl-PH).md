# 菲律宾语本地化翻译规则 (Filipino/Tagalog Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `tl-PH/` 目录 → 菲律宾语翻译
  - `en-US/` 目录 → 英文内容
- 标准路径结构：
 例如

  ```
  src/language/locales/
  ├── en-US.json    # 英文文案
  ├── tl-PH.json    # 菲律宾语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和菲律宾语文件
- 按照文件名称的字母排序与英文文件逐个进行对比

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`Kabuuang {count} na mensahe`
  - 人名变量：无需空格，如：`Si {userName}`

## 2. 菲律宾语语言规范

### 2.1 语体系统

- 对用户的提示：使用礼貌语气（po/opo）
  - 英文：`"Please check your email"`
  - 菲律宾语：`"Pakicheck po ang inyong email"`
- 系统状态提示：使用简洁表达
  - 英文：`"Loading..."`
  - 菲律宾语：`"Naglo-load..."`
- 错误信息：使用完整的句式
  - 英文：`"Invalid email format"`
  - 菲律宾语：`"Hindi tama ang format ng email"`

### 2.2 动词语法规则

- 过去式：nag-, naka-, na-
- 现在式：nag-, naka-, na-
- 将来式：mag-, maka-, ma-
- 被动语态：-in, -an, i-

示例：

```json
{
  "MESSAGE": {
    "SENT": "Naipadala na ang mensahe",
    "RECEIVED": "Nakatanggap ng bagong mensahe",
    "LOCATION": "Pinadala ang mensahe mula sa Manila",
    "TOOL": "Pinroseso ng sistema"
  }
}
```

### 2.3 疑问和确认表达

- ba：一般疑问
- po：礼貌用语
- opo：礼貌确认
- sige：同意

示例：

```json
{
  "CONFIRM": {
    "SAVE": "I-save po ba?",
    "INFO": "Na-update na po ang settings",
    "CHECK": "Tignan natin"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 菲律宾语标点符号：
  - 句号：.
  - 逗号：,
  - 括号：( )
  - 引号：" "
  - 问号：?
  - 感叹号：!
- 半角符号使用场景：
  - 英数字
  - 变量
  - URL/邮箱地址
- 混排规则：
  - 菲律宾语和英文之间加空格
  - 菲律宾语和数字之间加空格
  - 数字和单位之间加空格

### 3.2 数字表达规则

- 数量单位：
  - 一般数量：na/ng + 名词
  - 人数：na tao
  - 时间：minuto, oras, araw
- 数字使用规则：
  - 1-10：可使用菲律宾语数字（isa, dalawa, tatlo等）
  - 10以上：使用阿拉伯数字
- 示例：

  ```json
  {
    "COUNT": {
      "MESSAGES": "{count} na mensahe",
      "USERS": "{count} na user",
      "TIME": "{minutes} na minuto"
    }
  }
  ```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "Mag-sign in",
    "LOGOUT": "Mag-sign out",
    "SETTINGS": "Settings",
    "DASHBOARD": "Dashboard",
    "PROFILE": "Profile",
    "NOTIFICATION": "Notification"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "Online",
    "OFFLINE": "Offline",
    "BUSY": "Busy",
    "AWAY": "Away",
    "AVAILABLE": "Available"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "I-save",
    "CANCEL": "Cancel",
    "DELETE": "Delete",
    "EDIT": "I-edit",
    "UPDATE": "I-update",
    "LOADING": "Naglo-load",
    "PROCESSING": "Pinoproseso",
    "PLEASE_WAIT": "Sandali lang po",
    "COMPLETED": "Tapos na"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "BetFugu" 保持不变（保持英文）
- "Jackpot" 可翻译为 "Jackpot" 或保持英文
- 技术术语保持不变：API、URL、Email等
- 其他品牌名称保持原文：Facebook、Google、Apple等

### 5.2 语序结构转换

1. 时间地点在前：
   - 英文：`"Message sent at {time} in {location}"`
   - 菲律宾语：`"Naipadala ang mensahe noong {time} sa {location}"`

2. 动作主体和接受者：
   - 英文：`"Assigned to {assignee} by {user}"`
   - 菲律宾语：`"Na-assign ni {user} kay {assignee}"`

3. 状态变化描述：
   - 英文：`"Status changed from {old} to {new}"`
   - 菲律宾语：`"Nabago ang status mula sa {old} patungo sa {new}"`

## 6. 常见错误示例

### 6.1 错误的翻译示例

```json
{
  "WRONG": {
    "SPACING": "Angsistemagnonline",           // 错误：缺少空格
    "CORRECT": "Ang sistema ay online",        // 正确：适当的空格

    "GRAMMAR": "Magbabago setting",            // 错误：语法错误
    "CORRECT": "Magbabago ang setting",        // 正确：正确的语法

    "POLITENESS": "Baguhin password",          // 错误：缺少礼貌用语
    "CORRECT": "Pakibago po ang password"      // 正确：使用礼貌用语
  }
}
```

### 6.2 常见错误修正

1. 变量顺序错误：
   - 错误：`"Nag-assign ang {assignee} sa {team} ng {user}"`
   - 正确：`"Na-assign ni {user} sa {team} si {assignee}"`

2. 礼貌用语使用错误：
   - 错误：`"Binago ang password"`
   - 正确：`"Na-update na po ang password"`

3. 语序结构错误：
   - 错误：`"Error nangyari: database connection"`
   - 正确：`"Nagkaproblema sa database connection"`

## 7. 菲律宾语特殊语法规则

### 7.1 冠词使用

- "ang" - 特定名词前
- "ng" - 所有格标记
- "sa" - 位置/方向标记

### 7.2 动词焦点系统

- Actor Focus (mag-, um-, mang-)
- Object Focus (-in, -an)
- Beneficiary Focus (i-, ipag-)

### 7.3 常用连接词

- "at" - 和
- "o" - 或者
- "pero" - 但是
- "kaya" - 所以

## 8. UI长度控制规则

### 8.1 文案长度要求

- **核心原则**：翻译文案长度应与英文原文保持基本一致
- **目标**：避免因文案长度差异导致的界面布局错乱
- **控制范围**：所有UI可见文案，特别是按钮、标签、标题等

### 8.2 长度控制策略

1. **按钮文案**：
   - 优先使用简洁表达
   - 英文：`"Save"` → 菲律宾语：`"Save"` (保持英文)
   - 英文：`"Cancel"` → 菲律宾语：`"Cancel"` (保持英文)
   - 英文：`"Delete"` → 菲律宾语：`"Delete"` (保持英文)

2. **技术术语保持英文**：
   - 系统术语：Login, Settings, Profile, Dashboard
   - 状态术语：Online, Offline, Loading, Error
   - 操作术语：Update, Refresh, Connect, Sync

3. **标题和标签**：
   - 使用缩写或简化表达
   - 避免冗长的解释性文字
   - 保持核心信息完整

### 8.3 长度检查标准

- **字符数控制**：菲律宾语翻译不应超过英文原文字符数的120%
- **视觉宽度**：考虑菲律宾语字符的视觉宽度
- **容器适配**：确保翻译文案能在原UI容器中正常显示

### 8.4 特殊情况处理

1. **必要的长翻译**：
   - 使用省略号 "..." 处理超长文案
   - 分行显示（如果UI支持）
   - 使用工具提示显示完整信息

2. **缩写策略**：
   - 使用常见的菲律宾语缩写
   - 保持关键信息不丢失
   - 确保用户理解无障碍

3. **混合策略**：
   - 关键词使用英文，描述使用菲律宾语
   - 品牌名称保持英文
   - 技术术语保持英文

### 8.5 质量检查清单

- [ ] 翻译文案长度在合理范围内
- [ ] 在不同屏幕尺寸下UI显示正常
- [ ] 按钮文字未被截断
- [ ] 标题在容器内完整显示
- [ ] 关键信息未丢失
- [ ] 用户体验未受影响
