# 日语本地化翻译规则 (Japanese Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `ja/` 目录 → 日语翻译
  - `en/` 目录 → 英文内容
- 标准路径结构：

  ```
  app/javascript/dashboard/i18n/locale/
  ├── en/    # 英文文案
  ├── ja/    # 日语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和日语文件

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`共有 {count} 件`
  - 词组变量：无需空格，如：`{userName}様`

## 2. 日语语言规范

### 2.1 敬语体系

- 对用户的提示：使用です/ます体（礼貌语）
  - 英文：`"Please check your email"`
  - 日语：`"メールをご確認ください"`
- 系统状态提示：使用简体
  - 英文：`"Loading..."`
  - 日语：`"読み込み中"`
- 错误信息：使用です/ます体
  - 英文：`"Invalid email format"`
  - 日语：`"メールアドレスの形式が正しくありません"`

### 2.2 助词使用规则

- は：主题引入
- が：新信息引入
- を：动作对象
- に：时间、场所、目标
- で：手段、场所
- へ：方向、目标

示例：

```json
{
  "MESSAGE": {
    "SENT": "メッセージが送信されました",
    "RECEIVED": "新しいメッセージを受信しました",
    "LOCATION": "東京支店にメッセージを送信",
    "TOOL": "システムで処理しました"
  }
}
```

### 2.3 语气词使用规则

- ね：寻求确认
- よ：提供信息
- か：疑问
- よね：柔和的确认

示例：

```json
{
  "CONFIRM": {
    "SAVE": "保存しますか？",
    "INFO": "設定を変更しましたよ",
    "CHECK": "確認してみましょうね"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 日语标点符号（全角）：
  - 句号：。
  - 逗号：、
  - 括号：（）
  - 引号：「」『』
- 半角符号使用场景：
  - 英数字
  - 变量
  - URL/邮箱地址
- 混排规则：
  - 日语和英文之间加空格
  - 日语和数字之间加空格
  - 数字和单位之间加空格

### 3.2 数字表达规则

- 数量单位：
  - 一般数量：個、件、通
  - 人数：名、人
  - 时间：分、時間、日
- 数字使用规则：
  - 1-10：使用日语数字（一つ、二つ等）
  - 10以上：使用阿拉伯数字
- 示例：

  ```json
  {
    "COUNT": {
      "MESSAGES": "メッセージ {count} 通",
      "USERS": "ユーザー {count} 名",
      "TIME": "所要時間 {minutes} 分"
    }
  }
  ```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "ログイン",
    "LOGOUT": "ログアウト",
    "SETTINGS": "設定",
    "DASHBOARD": "ダッシュボード",
    "PROFILE": "プロフィール",
    "NOTIFICATION": "通知"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "オンライン",
    "OFFLINE": "オフライン",
    "BUSY": "取り込み中",
    "AWAY": "離席中",
    "AVAILABLE": "対応可能"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "保存",
    "CANCEL": "キャンセル",
    "DELETE": "削除",
    "EDIT": "編集",
    "UPDATE": "更新",
    "LOADING": "読み込み中",
    "PROCESSING": "処理中",
    "PLEASE_WAIT": "お待ちください",
    "COMPLETED": "完了しました"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "Chatwoot" 改为 "Talka"
- "chatwoot" 改为 "talka"
- Webhook,webhook 不用翻译， 保持不变
- 其他品牌名称保持原文：Facebook、GitHub、Twitter等

### 5.2 语序结构转换

1. 时间地点在前：
   - 英文：`"Message sent at {time} in {location}"`
   - 日语：`"{location}にて{time}にメッセージを送信しました"`

2. 动作主体和接受者：
   - 英文：`"Assigned to {assignee} by {user}"`
   - 日语：`"{user}が{assignee}に割り当てました"`

3. 状态变化描述：
   - 英文：`"Status changed from {old} to {new}"`
   - 日语：`"ステータスが{old}から{new}に変更されました"`

## 6. 常见错误示例

### 6.1 错误的翻译示例

```json
{
  "WRONG": {
    "SPACING": "システムはオンラインです",      // 错误：缺少空格
    "CORRECT": "システムは オンライン です",    // 正确：适当的空格

    "PARTICLE": "設定を変更は完了",            // 错误：助词使用错误
    "CORRECT": "設定の変更が完了しました",      // 正确：正确的助词

    "KEIGO": "設定を変更した",                // 错误：缺少敬语
    "CORRECT": "設定を変更いたしました"        // 正确：使用敬语
  }
}
```

### 6.2 常见错误修正

1. 变量顺序错误：
   - 错误：`"{assignee}が{team}に{user}を割り当てました"`
   - 正确：`"{user}が{team}を通じて{assignee}に割り当てました"`

2. 敬语使用错误：
   - 错误：`"パスワードを変更した"`
   - 正确：`"パスワードを変更しました"`

3. 语序结构错误：
   - 错误：`"エラーが発生：データベース接続"`
   - 正确：`"データベース接続でエラーが発生しました"`
