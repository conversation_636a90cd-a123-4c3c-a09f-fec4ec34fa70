---
description: 国际化韩语翻译
globs: *.json
alwaysApply: false
---
# 韩语本地化翻译规则 (Korean Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `ko/` 目录 → 韩语翻译
  - `en/` 目录 → 英文内容
- 标准路径结构：

  ```
  app/javascript/dashboard/i18n/locale/
  ├── en/    # 英文文案
  ├── ko/    # 韩语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和韩语文件
- 按照文件名称的字母排序与英文文件逐个进行对比

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`총 {count} 개`
  - 词组变量：无需空格，如：`{userName}님`

## 2. 韩语语言规范

### 2.1 敬语体系

- 对用户的提示：使用합니다/습니다体（礼貌语）
  - 英文：`"Please check your email"`
  - 韩语：`"이메일을 확인해 주세요"`
- 系统状态提示：使用简体
  - 英文：`"Loading..."`
  - 韩语：`"로딩 중"`
- 错误信息：使用합니다/습니다体
  - 英文：`"Invalid email format"`
  - 韩语：`"이메일 형식이 올바르지 않습니다"`

### 2.2 助词使用规则

- 은/는：主题标记
- 이/가：主语标记
- 을/를：宾语标记
- 에：位置、时间
- 에서：场所、起点
- (으)로：方向、手段

示例：

```json
{
  "MESSAGE": {
    "SENT": "메시지를 전송했습니다",
    "RECEIVED": "새로운 메시지를 수신했습니다",
    "LOCATION": "서울 지점에서 메시지를 전송",
    "TOOL": "시스템으로 처리했습니다"
  }
}
```

### 2.3 语气词使用规则

- 요：礼貌语气
- 까：疑问
- 네：确认
- 죠：柔和的确认

示例：

```json
{
  "CONFIRM": {
    "SAVE": "저장하시겠습니까?",
    "INFO": "설정이 변경되었어요",
    "CHECK": "확인해 보시죠"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 韩语标点符号：
  - 句号：。或.
  - 逗号：，或,
  - 括号：（）
  - 引号：" "
- 半角符号使用场景：
  - 英数字
  - 变量
  - URL/邮箱地址
- 混排规则：
  - 韩语和英文之间加空格
  - 韩语和数字之间加空格
  - 数字和单位之间加空格

### 3.2 数字表达规则

- 数量单位：
  - 一般数量：개、건、통
  - 人数：명
  - 时间：분、시간、일
- 数字使用规则：
  - 1-10：可使用韩语数字（하나、둘 等）
  - 10以上：使用阿拉伯数字
- 示例：

  ```json
  {
    "COUNT": {
      "MESSAGES": "메시지 {count} 통",
      "USERS": "사용자 {count} 명",
      "TIME": "소요 시간 {minutes} 분"
    }
  }
  ```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "로그인",
    "LOGOUT": "로그아웃",
    "SETTINGS": "설정",
    "DASHBOARD": "대시보드",
    "PROFILE": "프로필",
    "NOTIFICATION": "알림"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "온라인",
    "OFFLINE": "오프라인",
    "BUSY": "바쁨",
    "AWAY": "자리비움",
    "AVAILABLE": "대화 가능"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "저장",
    "CANCEL": "취소",
    "DELETE": "삭제",
    "EDIT": "편집",
    "UPDATE": "업데이트",
    "LOADING": "로딩 중",
    "PROCESSING": "처리 중",
    "PLEASE_WAIT": "잠시만 기다려 주세요",
    "COMPLETED": "완료되었습니다"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "Chatwoot" 改为 "Talka"（保持英文）
- "chatwoot" 改为 "talka"（保持英文）
- Webhook,webhook 不用翻译， 保持不变
- 其他品牌名称保持原文：Facebook、GitHub、Twitter等

### 5.2 语序结构转换

1. 时间地点在前：
   - 英文：`"Message sent at {time} in {location}"`
   - 韩语：`"{location}에서 {time}에 메시지를 전송했습니다"`

2. 动作主体和接受者：
   - 英文：`"Assigned to {assignee} by {user}"`
   - 韩语：`"{user}님이 {assignee}님에게 할당했습니다"`

3. 状态变化描述：
   - 英文：`"Status changed from {old} to {new}"`
   - 韩语：`"상태가 {old}에서 {new}(으)로 변경되었습니다"`

## 6. 常见错误示例

### 6.1 错误的翻译示例

```json
{
  "WRONG": {
    "SPACING": "시스템이온라인입니다",      // 错误：缺少空格
    "CORRECT": "시스템이 온라인 입니다",    // 正确：适当的空格

    "PARTICLE": "설정을변경은완료",         // 错误：助词使用错误
    "CORRECT": "설정 변경이 완료되었습니다",  // 正确：正确的助词

    "HONORIFIC": "설정을 변경했다",         // 错误：缺少敬语
    "CORRECT": "설정을 변경했습니다"        // 正确：使用敬语
  }
}
```

### 6.2 常见错误修正

1. 变量顺序错误：
   - 错误：`"{assignee}가 {team}에 {user}를 할당했습니다"`
   - 正确：`"{user}님이 {team}을 통해 {assignee}님에게 할당했습니다"`

2. 敬语使用错误：
   - 错误：`"비밀번호를 변경했다"`
   - 正确：`"비밀번호를 변경했습니다"`

3. 语序结构错误：
   - 错误：`"오류 발생: 데이터베이스 연결"`
   - 正确：`"데이터베이스 연결 중 오류가 발생했습니다"`
