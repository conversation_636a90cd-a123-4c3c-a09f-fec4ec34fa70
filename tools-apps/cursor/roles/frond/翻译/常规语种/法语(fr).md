<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-15 10:32:48
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-15 10:55:41
 * @FilePath     : /tools-apps/cursor/roles/frond/翻译/法语(fr).md
 * @Description  : Règles d'internationalisation pour le français
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-15 10:32:48
-->

# 法语(fr)国际化翻译规则

请按照以下规则翻译这些文件中的文本内容：

## 基本规则

1. 自动检测法语类型：
   - 根据文件路径确定目标法语变体
   - fr_FR/ 或 fr-FR/ 目录 → 法语(法国)
   - fr_CA/ 或 fr-CA/ 目录 → 法语(加拿大)
   - fr_BE/ 或 fr-BE/ 目录 → 法语(比利时)
   - fr_CH/ 或 fr-CH/ 目录 → 法语(瑞士)

2. 默认行为：
   - 如果用户没有明确指定法语变体
   - 默认使用法国法语(fr_FR)
   - 根据地区特点调整翻译

3. 文件结构示例：

   ```
   app/javascript/dashboard/i18n/locale/
   ├── en/      # 英文内容
   ├── fr_FR/   # 法语(法国)内容
   └── fr_CA/   # 法语(加拿大)内容
   ```

## 基础翻译规则

1. 内容转换
   - 将英文文本翻译成法语
   - 保留代码变量，如 `{conversationCount}`
   - 保持现有法语文本不变
   - 保持数字不变

2. JSON格式标准
   - 保持JSON格式有效性
   - 确保键值对完整性
   - 保持字段名称、顺序和结构与源文件相同

3. 专有名词保留
   - 保持品牌名称不变：Facebook、Github、Twitter、WhatsApp、Google、Telegram等

4. 标点符号规则
   - 法语文本中使用法语标点
     - 双标点符号前加空格(: ; ! ? » )
     - 单标点符号前不加空格(. , ...)
   - 法语引号：« 文本 »（内部有空格）
   - 混合语言间距规则：
     - 在法语和英语之间添加空格
     - 在数字和法语文本之间添加空格
     - 在数字和单位之间添加空格

   示例：
   错误：这是一个Chat Widget设置页面,with5options.
   正确：这是一个 « Chat Widget » 设置页面，有 5 个选项。

5. 变量处理规则
   - 保持变量大小写敏感
   - 变量间距规则：
     - 独立变量周围添加空格
     - 短语中的变量不需要额外空格

   示例：
   - 独立变量：`有 {count} 条消息`
   - 短语变量：`{userName} 的设置`

## 风格要求

1. 一致性
   - 与现有法语翻译保持一致的语言风格
   - 使用统一的技术术语翻译
   - 保持语气和语调一致

2. 格式标准
   - 确保引号正确配对
   - 保持一致的缩进
   - 避免双引号内嵌套双引号

## 特殊处理

1. 文化差异注意事项
   - 避免文化敏感内容(宗教、政治等)
   - 处理数字格式差异(法语使用逗号作为小数点)
   - 日期时间格式本地化(法语使用DD/MM/YYYY格式)
   - 货币单位转换显示(法国使用€符号)
   - 度量衡单位转换(使用公制)

2. 语法表达规范
   - 保持简洁直接的表达
   - 优先使用主动语态
   - 避免过长的复合句
   - 技术术语保持一致性
   - 符合目标语言地区的习惯用法

3. 文件比较要求
   - 翻译前比较英文和法语文件
   - 添加缺失的法语翻译字段
   - 确保字段顺序匹配
   - 保持字段名称相同

4. 语言逻辑差异

   ### 语法转换规则

   #### 1. 动作主体和接收者

   - 英语的"A assigned to B"结构在法语中应转换为"B assigné à A"或"A assigné par B"
   - 保持变量语义角色：
     - 正确：`"{assignee} assigné par {user}"`
     - 错误：`"{user} assigné par {assignee}"`
   - 对于多个参与者，按照法语逻辑组织词序
   - 确保翻译在保持原始语义的同时保持自然的法语表达

   示例：
   英语：`"Assigned to {assignee} via {team} by {user}"`
   正确翻译：`"Assigné à {assignee} via {team} par {user}"`
   错误翻译：`"Par {user} assigné à {assignee} via {team}"`

   #### 2. 时间表达转换

   - 将"month/day/year"转换为"day/month/year"
   - 将"AM/PM"转换为24小时制
   - 翻译星期几和月份名称

   示例：
   英语：`"Monday, December 25, 2023 at 3:00 PM"`
   法语：`"Lundi 25 décembre 2023 à 15h00"`

   #### 3. 地点描述顺序

   - 法语中地点描述通常从小到大排列
   - 保持地址元素完整性

   示例：
   英语：`"Zhangjiang Hi-Tech Park, Pudong, Shanghai, China"`
   法语：`"Parc Hi-Tech de Zhangjiang, Pudong, Shanghai, Chine"`

   #### 4. 被动/主动语态转换

   - 法语在某些情况下比英语更常使用被动语态
   - 保持语义重点不变

   示例：
   英语：`"The administrator restarted the system"`
   法语：`"Le système a été redémarré par l'administrateur"` 或 `"L'administrateur a redémarré le système"`

   #### 5. 修饰语位置

   - 法语形容词通常放在名词后面(与英语相反)
   - 长修饰语使用关系结构

   示例：
   英语：`"The purchased laptop"`
   法语：`"L'ordinateur portable acheté"`

   #### 6. 数量表达转换

   - 将数量表达适应法语习惯
   - 正确处理复数形式
   - 转换近似数值表达

   示例：
   英语：`"three books"`
   法语：`"trois livres"`
   英语：`"dozens of users"`
   法语：`"des dizaines d'utilisateurs"`

   #### 7. 比较结构转换

   - 将英语比较结构转换为法语等效结构
   - 调整最高级形式
   - 转换同级比较

   示例：
   英语：`"busier than yesterday"`
   法语：`"plus occupé qu'hier"`

   #### 8. 否定形式转换

   - 调整否定位置(ne...pas结构)
   - 处理双重否定
   - 处理否定转移情况

   示例：
   英语：`"I don't think he will come"`
   法语：`"Je ne pense pas qu'il vienne"`

   #### 9. 习惯用语转换

   - 为英语习惯用语找到法语等效表达
   - 保持语义而非字面翻译
   - 调整文化特定表达

   示例：
   英语：`"break the ice"`
   法语：`"briser la glace"`

## 翻译示例

### 示例1：基本翻译

原文：

```json
{
  "CONVERSATION": {
    "you_have_new_message": "You have {count} new messages",
    "app_name": "Chatwoot"
  }
}
```

译文:

```json
{
  "CONVERSATION": {
    "you_have_new_message": "Vous avez {count} nouveaux messages",
    "app_name": "Chatwoot"
  }
}
```

### 示例2：处理缺失字段

原英文文件：

```json
{
  "INBOX": {
    "SETTINGS": {
      "TITLE": "Inbox Settings",
      "UPDATE": {
        "SUCCESS": "Inbox settings updated successfully",
        "ERROR": "Couldn't update inbox settings",
        "RETRY": "Please try again"
      },
      "AUTO_ASSIGNMENT": {
        "ENABLED": "Enabled",
        "DISABLED": "Disabled"
      }
    }
  }
}
```

当前法语文件：

```json
{
  "INBOX": {
    "SETTINGS": {
      "TITLE": "Paramètres de la boîte de réception",
      "UPDATE": {
        "SUCCESS": "Paramètres de la boîte de réception mis à jour avec succès",
        "ERROR": "Impossible de mettre à jour les paramètres de la boîte de réception"
      }
    }
  }
}
```

正确的法语译文（补充缺失字段）：

```json
{
  "INBOX": {
    "SETTINGS": {
      "TITLE": "Paramètres de la boîte de réception",
      "UPDATE": {
        "SUCCESS": "Paramètres de la boîte de réception mis à jour avec succès",
        "ERROR": "Impossible de mettre à jour les paramètres de la boîte de réception",
        "RETRY": "Veuillez réessayer"
      },
      "AUTO_ASSIGNMENT": {
        "ENABLED": "Activé",
        "DISABLED": "Désactivé"
      }
    }
  }
}
```

### 示例3：专业术语统一

原文：

```json
{
  "DASHBOARD": {
    "AGENT_INBOX": "Agent Inbox",
    "CONVERSATION_WIDGET": "Chat Widget Settings",
    "ACTIVE_CONVERSATIONS": "Active Conversations"
  }
}
```

译文:

```json
{
  "DASHBOARD": {
    "AGENT_INBOX": "Boîte de réception de l'agent",
    "CONVERSATION_WIDGET": "Paramètres du widget de discussion",
    "ACTIVE_CONVERSATIONS": "Conversations actives"
  }
}
```
