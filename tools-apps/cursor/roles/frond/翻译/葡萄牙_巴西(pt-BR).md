<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-10 15:42:19
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-10 15:57:23
 * @FilePath     : /Afeng/翻译/葡萄牙_巴西(pt-BR).md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-10 15:42:19
-->

# 巴西葡萄牙语本地化翻译规则 (Brazilian Portuguese Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `pt-BR/` 目录 → 巴西葡萄牙语翻译
  - `en-US/` 目录 → 英文内容
- 标准路径结构：
 例如

  ```
  src/language/locales/
  ├── en-US.json    # 英文文案
  ├── pt-BR.json    # 巴西葡萄牙语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和巴西葡萄牙语文件
- 按照文件名称的字母排序与英文文件逐个进行对比

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 独立变量：前后加空格，如：`Total de {count} mensagens`
  - 人名变量：根据语境处理，如：`{userName} enviou`

## 2. 巴西葡萄牙语语言规范

### 2.1 语体系统

- 对用户的提示：使用礼貌用语（por favor, gentilmente）
  - 英文：`"Please check your email"`
  - 巴西葡萄牙语：`"Por favor, verifique seu e-mail"`
- 系统状态提示：使用简洁表达
  - 英文：`"Loading..."`
  - 巴西葡萄牙语：`"Carregando..."`
- 错误信息：使用完整的句式
  - 英文：`"Invalid email format"`
  - 巴西葡萄牙语：`"Formato de e-mail inválido"`

### 2.2 动词时态和语态规则

- 现在时：使用动词原形变位
- 过去时：pretérito perfeito/imperfeito
- 将来时：futuro do presente
- 命令式：用于按钮和操作指令

示例：

```json
{
  "MESSAGE": {
    "SENT": "Mensagem enviada",
    "RECEIVED": "Nova mensagem recebida",
    "LOCATION": "Mensagem enviada do Rio de Janeiro",
    "TOOL": "Processado pelo sistema"
  }
}
```

### 2.3 疑问和确认表达

- 疑问词：O que, Como, Quando, Onde
- 确认：Sim/Não, Confirmar, OK
- 礼貌表达：Por favor, Obrigado(a)

示例：

```json
{
  "CONFIRM": {
    "SAVE": "Deseja salvar?",
    "INFO": "Configurações atualizadas",
    "CHECK": "Vamos verificar"
  }
}
```

## 3. 格式规范

### 3.1 标点符号规则

- 巴西葡萄牙语标点符号：
  - 句号：.
  - 逗号：,
  - 括号：( )
  - 引号：" " 或 « »
  - 问号：?
  - 感叹号：!
  - 破折号：—
- 半角符号使用场景：
  - 英数字
  - 变量
  - URL/邮箱地址
- 混排规则：
  - 葡萄牙语和英文之间加空格
  - 葡萄牙语和数字之间加空格
  - 数字和单位之间加空格

### 3.2 数字表达规则

- 数量单位：
  - 一般数量：quantidade/número
  - 人数：pessoas/usuários
  - 时间：minutos, horas, dias
- 数字使用规则：
  - 1-10：可使用葡萄牙语数字（um, dois, três等）
  - 10以上：使用阿拉伯数字
- 巴西特色：
  - 货币：R$ (Real brasileiro)
  - 小数点：使用逗号（,）而非点（.）
  - 千位分隔符：使用点（.）

示例：

```json
{
  "COUNT": {
    "MESSAGES": "{count} mensagens",
    "USERS": "{count} usuários",
    "TIME": "{minutes} minutos",
    "PRICE": "R$ {amount}"
  }
}
```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "Entrar",
    "LOGOUT": "Sair",
    "SETTINGS": "Configurações",
    "DASHBOARD": "Painel",
    "PROFILE": "Perfil",
    "NOTIFICATION": "Notificação"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "Online",
    "OFFLINE": "Offline",
    "BUSY": "Ocupado",
    "AWAY": "Ausente",
    "AVAILABLE": "Disponível"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "Salvar",
    "CANCEL": "Cancelar",
    "DELETE": "Excluir",
    "EDIT": "Editar",
    "UPDATE": "Atualizar",
    "LOADING": "Carregando",
    "PROCESSING": "Processando",
    "PLEASE_WAIT": "Aguarde",
    "COMPLETED": "Concluído"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "BetFugu" 保持不变（保持英文）
- "Jackpot" 可翻译为 "Jackpot" 或保持英文
- 技术术语保持不变：API、URL、Email等
- 其他品牌名称保持原文：Facebook、Google、Apple等

### 5.2 语序结构转换

巴西葡萄牙语基本语序：

- 主谓宾结构（SVO）：与英语相似
- 形容词通常在名词后面
- 代词位置比较灵活

语序转换示例：

```json
{
  "EXAMPLE": {
    "ENGLISH": "New message",
    "PORTUGUESE": "Nova mensagem"
  }
}
```

### 5.3 性别一致性

葡萄牙语有阳性和阴性区分：

- 男性用户：usuário, jogador
- 女性用户：usuária, jogadora
- 中性表达：pessoa, participante

示例：

```json
{
  "GENDER": {
    "PLAYER_MALE": "jogador",
    "PLAYER_FEMALE": "jogadora",
    "PLAYER_NEUTRAL": "participante"
  }
}
```

## 6. 常见错误示例

### 6.1 错误翻译

❌ 错误示例：

```json
{
  "LOGIN": "Fazer login",      // 过于冗长
  "SAVE": "Salvando",         // 使用现在进行时而非命令式
  "ERROR": "Um erro aconteceu" // 过于口语化
}
```

✅ 正确翻译：

```json
{
  "LOGIN": "Entrar",
  "SAVE": "Salvar",
  "ERROR": "Erro encontrado"
}
```

### 6.2 格式错误修正

❌ 错误格式：

- 变量处理：`Total{count}mensagens` → `Total de {count} mensagens`
- 标点符号：`Tem certeza。` → `Tem certeza?`
- 性别一致性：`novo usuária` → `nova usuária`

## 7. 巴西葡萄牙语特殊语法规则

### 7.1 动词变位

- 第一人称：eu faço, eu vou
- 第二人称：você faz, você vai (巴西式)
- 第三人称：ele/ela faz, ele/ela vai

注意：巴西葡萄牙语更多使用"você"而非"tu"

### 7.2 代词使用

- 直接宾语代词：me, te, o/a, nos, os/as
- 间接宾语代词：me, te, lhe, nos, lhes
- 巴西特色：代词常放在动词前

示例：

```json
{
  "PRONOUNS": {
    "SEND_TO_ME": "me envie",
    "SHOW_HIM": "mostre para ele",
    "TELL_US": "nos conte"
  }
}
```

### 7.3 巴西俚语和表达

谨慎使用巴西特有表达：

- "Legal" (cool/nice) - 可在非正式场合使用
- "Beleza" (alright/OK) - 避免在正式界面使用
- "Valeu" (thanks) - 太过口语化，避免使用

## 8. UI长度控制规则

### 8.1 文案长度要求

- **核心原则**：翻译文案长度应与英文原文保持基本一致
- **目标**：避免因文案长度差异导致的界面布局错乱
- **控制范围**：所有UI可见文案，特别是按钮、标签、标题等

### 8.2 长度控制策略

1. **按钮文案**：优先使用简洁表达
   - `"SAVE"` → `"Salvar"` 而非 `"Salvar arquivo"`
   - `"LOGIN"` → `"Entrar"` 而非 `"Fazer login"`

2. **技术术语处理**：
   - 系统术语：适度本地化
   - 游戏术语：保持英文或使用简短翻译
   - 操作术语：使用简洁的命令式动词

3. **标题和标签**：使用缩写或简化表达
   - 避免冗长的介词短语
   - 优先使用名词而非完整句子

### 8.3 长度检查标准

- **字符数控制**：葡萄牙语翻译不应超过英文原文字符数的130%
- **视觉宽度**：考虑葡萄牙语单词平均长度较英文长
- **容器适配**：确保在不同屏幕尺寸下正常显示

### 8.4 特殊情况处理

1. **必要的长翻译**：
   - 使用换行符合理分割
   - 采用缩写策略
   - 考虑上下文提示

2. **缩写策略**：
   - 保留关键动词
   - 省略不必要的介词
   - 使用常见缩写形式

3. **混合策略**：
   - 技术词汇适度保持英文
   - 动作词汇使用葡萄牙语
   - 根据巴西用户习惯调整

### 8.5 质量检查清单

翻译完成后进行以下检查：

- [ ] 字符数是否在控制范围内（≤130%）
- [ ] 在不同设备上显示是否正常
- [ ] 是否保持了原文的核心信息
- [ ] 用户理解是否无障碍
- [ ] 是否符合巴西葡萄牙语表达习惯
- [ ] 性别一致性是否正确
- [ ] 按钮和标签是否在容器内正常显示
- [ ] 响应式布局是否受到影响

## 9. 巴西特有考虑事项

### 9.1 文化适应性

- 考虑巴西的多元文化背景
- 使用包容性语言
- 避免可能引起地域争议的表达
- 考虑巴西的社会经济多样性

### 9.2 本地化特色

- 货币格式：R$ 1.234,56
- 日期格式：dd/mm/aaaa
- 时间格式：24小时制（更常见）
- 电话格式：+55 (11) 9xxxx-xxxx

### 9.3 游戏本地化

- 平衡国际化和本地化需求
- 游戏术语优先考虑玩家习惯
- 保持与国际游戏社区的连接
- 考虑巴西游戏市场的特点

### 9.4 法律和合规

- 遵守巴西的数据保护法规（LGPD）
- 注意游戏相关的法律用语
- 确保年龄相关提示的准确性
- 考虑巴西的消费者权益法规

### 9.5 用户体验优化

- 符合巴西用户的使用习惯
- 考虑移动设备优先的使用模式
- 保持界面的直观性和友好性
- 确保翻译的一致性和自然度

### 9.6 地域差异处理

- 优先使用标准巴西葡萄牙语
- 避免过于地方化的表达
- 考虑不同地区的文化差异
- 保持全国范围的理解一致性
