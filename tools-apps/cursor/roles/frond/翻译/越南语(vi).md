# 越南语本地化翻译规则 (Vietnamese Localization Rules)

## 1. 基础规则

### 1.1 翻译文件路径规则

- 根据文件路径自动判断目标语言：
  - `vi/` 目录 → 越南语翻译
  - `en/` 目录 → 英文内容
- 标准路径结构：

  ```
  app/javascript/dashboard/i18n/locale/
  ├── en/    # 英文文案
  ├── vi/    # 越南语文案
  ```

### 1.2 文件格式规范

- 保持 JSON 格式有效性
- 确保键值对完整性
- 与英文文件保持相同的：
  - 字段名称
  - 字段顺序
  - 字段结构
- 翻译前必须对比同名的英文和越南语文件

### 1.3 变量处理规则

- 保留所有代码变量，如：`{conversationCount}`
- 保持变量大小写不变
- 变量空格处理：
  - 变量周围通常需要空格，如：`Có tổng cộng {count} tin nhắn`
  - 对于表示所有格或紧密连接的词组，可以不加空格，如：`{userName}đã gửi`

## 2. 越南语语言规范

### 2.1 礼貌表达

- 对用户的提示：使用礼貌语气（thường + 敬语词）
  - 英文：`"Please check your email"`
  - 越南语：`"Vui lòng kiểm tra email của bạn"`
- 系统状态提示：使用简洁语气
  - 英文：`"Loading..."`
  - 越南语：`"Đang tải..."`
- 错误信息：使用礼貌但明确的语气
  - 英文：`"Invalid email format"`
  - 越南语：`"Định dạng email không hợp lệ"`

### 2.2 越南语代词使用

- 使用 "bạn"（你）来称呼用户
- 使用 "chúng tôi"（我们）代表系统或团队
- 在正式场合可使用 "quý khách"（尊敬的客户）

示例：

```json
{
  "MESSAGE": {
    "SENT": "Tin nhắn của bạn đã được gửi",
    "RECEIVED": "Bạn đã nhận được tin nhắn mới",
    "SUPPORT": "Chúng tôi sẽ hỗ trợ bạn sớm nhất có thể",
    "FORMAL": "Quý khách vui lòng xác nhận thông tin"
  }
}
```

### 2.3 越南语语气词

- "nhé"：友好的请求或建议
- "đấy"/"đó"：提醒或强调
- "à"/"ạ"：礼貌的语气

示例：

```json
{
  "CONFIRM": {
    "SAVE": "Bạn có muốn lưu không?",
    "INFO": "Cài đặt đã được thay đổi rồi đấy",
    "CHECK": "Kiểm tra lại nhé"
  }
}
```

### 2.4 声调和发音符号

越南语有6个声调，确保正确使用：

- 不标调 (a)
- 锐音符 (á)
- 重音符 (à)
- 问号 (ả)
- 波浪符 (ã)
- 点号 (ạ)

必须保留所有声调和发音符号，不可简化。

## 3. 格式规范

### 3.1 标点符号规则

- 越南语标点符号（使用拉丁文标点）：
  - 句号：.
  - 逗号：,
  - 括号：()
  - 引号：""
- 标点使用规则：
  - 与前面的词无空格
  - 与后面的词加空格
- 混排规则：
  - 越南语和英文之间加空格
  - 越南语和数字之间通常加空格

### 3.2 数字表达规则

- 数字用法：
  - 直接使用阿拉伯数字，如：1, 2, 3
  - 数字和单位之间加空格
- 示例：

  ```json
  {
    "COUNT": {
      "MESSAGES": "{count} tin nhắn",
      "USERS": "{count} người dùng",
      "TIME": "{minutes} phút"
    }
  }
  ```

## 4. 专业术语规范

### 4.1 系统操作术语

```json
{
  "SYSTEM": {
    "LOGIN": "Đăng nhập",
    "LOGOUT": "Đăng xuất",
    "SETTINGS": "Cài đặt",
    "DASHBOARD": "Bảng điều khiển",
    "PROFILE": "Hồ sơ",
    "NOTIFICATION": "Thông báo"
  }
}
```

### 4.2 状态术语

```json
{
  "STATUS": {
    "ONLINE": "Trực tuyến",
    "OFFLINE": "Ngoại tuyến",
    "BUSY": "Đang bận",
    "AWAY": "Vắng mặt",
    "AVAILABLE": "Có sẵn"
  }
}
```

### 4.3 UI要素术语

```json
{
  "UI": {
    "SAVE": "Lưu",
    "CANCEL": "Hủy",
    "DELETE": "Xóa",
    "EDIT": "Chỉnh sửa",
    "UPDATE": "Cập nhật",
    "LOADING": "Đang tải",
    "PROCESSING": "Đang xử lý",
    "PLEASE_WAIT": "Vui lòng đợi",
    "COMPLETED": "Đã hoàn thành"
  }
}
```

## 5. 特殊处理规则

### 5.1 品牌名称处理

- "Chatwoot" 改为 "Talka"
- "chatwoot" 改为 "talka"
- "Chatwoot" 或 "chatwoot" 必须是整个单词出现才改为 "Talka" 或 "talka"
- Webhook,webhook 不用翻译， 保持不变
- 其他品牌名称保持原文：Facebook、GitHub、Twitter等

### 5.2 语序结构转换

1. 时间地点表达：
   - 英文：`"Message sent at {time} in {location}"`
   - 越南语：`"Vào lúc {time} tại {location}, tin nhắn đã được gửi"`
   - 规则：越南语中时间和地点通常放在句首，用"vào lúc"(时间)和"tại"(地点)引导

2. 动作完成表达：
   - 英文：`"Assigned to {assignee} by {user}"`
   - 越南语：`"{user} đã phân công cho {assignee}"`
   - 规则：越南语保持主谓宾结构，但强调动作发起者

3. 状态变化描述：
   - 英文：`"Status changed from {old} to {new}"`
   - 越南语：`"Trạng thái đã thay đổi từ {old} sang {new}"`
   - 规则：变化方向使用"từ...sang..."(从...到...)结构

### 5.3 越南语特有表达

1. 复数表达：
   - 越南语没有复数形式变化，通常使用数量词或修饰词表达复数
   - 英文：`"1 message" / "2 messages"`
   - 越南语：`"1 tin nhắn" / "2 tin nhắn"`

2. 时态表达：
   - 越南语通过上下文或时间副词表达时态，而不是动词变形
   - 过去：đã + 动词
   - 现在：đang + 动词
   - 将来：sẽ + 动词

3. 方位表达：
   - 越南语方位表达与英文相反
   - 英文：`"in the right corner"`
   - 越南语：`"ở góc bên phải"`

### 5.4 修饰词位置规则

越南语修饰词通常放在被修饰词之后，与英文相反：

- 形容词位置：
  - 英文：`"new message"` (形容词+名词)
  - 越南语：`"tin nhắn mới"` (名词+形容词)

- 指示词位置：
  - 英文：`"this account"` (指示词+名词)
  - 越南语：`"tài khoản này"` (名词+指示词)

- 数量词位置：
  - 英文：`"all messages"` (数量词+名词)
  - 越南语：`"tất cả tin nhắn"` (数量词+名词) 或 `"tin nhắn tất cả"` (名词+数量词)

```json
{
  "EXAMPLES": {
    "NEW_MESSAGE": "Tin nhắn mới",
    "OLD_PASSWORD": "Mật khẩu cũ",
    "THIS_MONTH": "Tháng này",
    "THREE_DAYS": "Ba ngày"
  }
}
```

### 5.5 量词使用规则

越南语中名词通常需要搭配特定的量词使用：

- 人物量词："người" (người khách hàng - 客户)
- 物品量词："cái" (cái điện thoại - 电话)
- 动物量词："con" (con mèo - 猫)
- 组织量词："tổ chức" (tổ chức công ty - 公司)

示例：

```json
{
  "QUANTIFIERS": {
    "USERS": "{count} người dùng",
    "DEVICES": "{count} cái thiết bị",
    "COMPANIES": "{count} công ty"
  }
}
```

### 5.6 连词和从句结构

越南语中的连词和从句结构：

- 因果关系：
  - 英文：`"because, so, therefore"`
  - 越南语：`"vì, do đó, vì vậy"`

- 条件关系：
  - 英文：`"if, when, unless"`
  - 越南语：`"nếu, khi, trừ khi"`

- 转折关系：
  - 英文：`"but, however, although"`
  - 越南语：`"nhưng, tuy nhiên, mặc dù"`

示例：

```json
{
  "CONJUNCTIONS": {
    "CAUSE": "Vì bạn đã nhấp vào liên kết, tài khoản đã được kích hoạt",
    "CONDITION": "Nếu bạn quên mật khẩu, vui lòng nhấp vào đây",
    "CONTRAST": "Giao dịch đã hoàn tất, tuy nhiên số tiền chưa được cập nhật"
  }
}
```

## 6. 文化表达差异

### 6.1 称谓系统

越南语有复杂的称谓系统，根据年龄、关系和社会地位不同：

- 通用称谓：
  - 一般用户："bạn" (你)
  - 尊敬称谓："quý khách" (尊敬的客户)
  - 正式称谓："ông/bà" (先生/女士)

- 系统自称：
  - 中性："hệ thống" (系统)
  - 拟人化："chúng tôi" (我们)

示例：

```json
{
  "ADDRESSING": {
    "GENERAL": "Bạn cần giúp đỡ gì?",
    "FORMAL": "Quý khách vui lòng xác nhận",
    "SYSTEM": "Hệ thống đang xử lý yêu cầu của bạn",
    "COMPANY": "Chúng tôi đã nhận được phản hồi của bạn"
  }
}
```

### 6.2 文化敏感表达

某些表达方式需要考虑文化因素：

- 颜色象征：
  - 红色在越南象征好运和繁荣
  - 白色可以与葬礼联系

- 数字象征：
  - 避免使用4（发音类似"死"）
  - 8和9是吉利数字

- 节日表达：
  - 越南新年："Tết Nguyên Đán"
  - 中秋节："Tết Trung Thu"

## 7. 越南语测试与验证

### 7.1 常见错误检查

```json
{
  "WRONG": {
    "SPACING": "Bạnđãnhậnđược",      // 错误：缺少空格
    "CORRECT": "Bạn đã nhận được",    // 正确：适当的空格
  }
}
```

### 7.2 声调错误

```json
{
  "WRONG": {
    "TONE": "Xin chao ban",      // 错误：缺少声调
    "CORRECT": "Xin chào bạn",    // 正确：有声调
  }
}
```

### 7.3 词序错误

```json
{
  "WRONG": {
    "ORDER": "Mới tin nhắn đã nhận được bạn",  // 错误：词序混乱
    "CORRECT": "Bạn đã nhận được tin nhắn mới"  // 正确：主语-动词-宾语
  }
}
```

## 8. 越南语缩写和口语表达

### 8.1 常见缩写处理

越南语中的缩写在正式和非正式场合使用存在显著差异：

- 正式文档中**不应使用**缩写形式
- 非正式内容（如聊天对话、社交媒体）可适当使用缩写

常见缩写对照：

```json
{
  "ABBREVIATIONS": {
    "FORMAL": {
      "không sao": "没关系/没问题",
      "làm ơn": "请（命令性）",
      "vui lòng": "请（礼貌性）",
      "tại vì": "因为",
      "thế nào": "怎么样"
    },
    "INFORMAL": {
      "ko": "không（不）",
      "ko sao": "không sao（没关系）",
      "đc": "được（可以）",
      "trc": "trước（之前）",
      "cảm ơn": "cám ơn（谢谢）"
    }
  }
}
```

### 8.2 口语化表达与书面语区别

- 口语表达通常更简洁，使用较多语气词
- 书面语更正式，句式更完整，用词更规范

示例：

```json
{
  "EXPRESSIONS": {
    "WRITTEN": "Vui lòng xác nhận email của bạn để hoàn tất đăng ký",
    "SPOKEN": "Bạn check email rồi xác nhận để đăng ký nhé"
  }
}
```

### 8.3 情感表达词

越南语中表达情感的特殊词汇：

- 表示惊讶："Ôi", "Trời ơi", "Ái chà"
- 表示高兴："Tuyệt vời", "Tốt quá"
- 表示失望："Chán quá", "Tiếc quá"

## 9. 越南语标点符号特殊用法

### 9.1 标点符号组合

越南语中特殊的标点组合用法：

- 问号与感叹号组合："?!" 表示惊讶的疑问
- 多重感叹号："!!!" 表示强烈情感（非正式场合使用）
- 省略号："..." 在越南语中使用频率较高，表示未完成的思想或停顿

```json
{
  "PUNCTUATION": {
    "SURPRISE_QUESTION": "Bạn đã làm việc đó rồi sao?!",
    "STRONG_EMOTION": "Tuyệt vời!!!",
    "PAUSING": "Tôi đang suy nghĩ..."
  }
}
```

### 9.2 标点符号与空格

越南语标点符号与空格的使用规则：

- 逗号、句号、冒号前**不加**空格
- 逗号、句号、冒号后**必须加**空格
- 开括号前加空格，开括号后不加空格
- 闭括号前不加空格，闭括号后加空格

### 9.3 段落与文本结构

越南语文本结构的特点：

- 段落间使用空行分隔
- 列表项使用"•"符号或数字+点号
- 引用文本使用引号，但不使用缩进

## 10. 互联网用语和技术术语翻译

### 10.1 技术术语翻译策略

技术术语的翻译策略：

1. 优先使用已被广泛接受的越南语技术术语
2. 如无标准翻译，可保留英文原词，但需注意加以解释
3. 对于新兴技术术语，遵循行业惯例

```json
{
  "TECH_TERMS": {
    "TRANSLATED": {
      "download": "tải xuống",
      "upload": "tải lên",
      "login": "đăng nhập",
      "logout": "đăng xuất",
      "user": "người dùng",
      "password": "mật khẩu"
    },
    "UNTRANSLATED": {
      "API": "API",
      "SDK": "SDK",
      "URL": "URL",
      "cache": "cache"
    }
  }
}
```

### 10.2 社交媒体术语

社交媒体常用术语的越南语表达：

```json
{
  "SOCIAL_MEDIA": {
    "like": "thích",
    "share": "chia sẻ",
    "comment": "bình luận",
    "follow": "theo dõi",
    "post": "bài đăng",
    "hashtag": "thẻ bắt đầu bằng #"
  }
}
```

### 10.3 越南语互联网流行语

越南互联网上的流行语和表达：

- "gất" - 非常好/厉害（来自"ghê gớm"）
- "xỉu" - 晕倒，表示某事太有趣/太惊人
- "gáy" - 吹嘘，炫耀

注意：互联网流行语变化快，仅在非正式内容中使用，正式产品界面应避免使用。

## 11. 地区差异处理

### 11.1 南北方言差异

越南南北方在词汇、发音和表达上存在明显差异：

- 北越（河内）：通常被视为标准越南语
- 南越（胡志明市）：在商业环境中也很常见

词汇差异示例：

```json
{
  "REGIONAL": {
    "NORTH": {
      "bicycle": "xe đạp",
      "plastic bag": "túi nilon",
      "pen": "bút bi"
    },
    "SOUTH": {
      "bicycle": "xe đạp đua",
      "plastic bag": "túi ny lông",
      "pen": "viết bi"
    }
  }
}
```

### 11.2 方言标准化策略

翻译时的方言处理原则：

1. 如无特殊要求，优先使用北越标准用词
2. 面向特定地区用户时，可考虑采用当地用词
3. 需要同时覆盖南北用户时，选择更通用或同时提供两种表达

### 11.3 地区敏感表达

某些表达在不同地区可能有不同含义或接受度：

- 称谓词："anh/chị"（北方）vs "anh/chị"（南方，但使用场景略有不同）
- 数字表达："chín mươi lăm"（北方）vs "chín mươi năm"（南方）

## 12. 语境敏感词汇

### 12.1 同一概念的多种翻译

根据语境不同，同一英文词可能有不同的越南语翻译：

```json
{
  "CONTEXT_SENSITIVE": {
    "HOME": {
      "website_nav": "Trang chủ",
      "physical_address": "Nhà",
      "return_button": "Về đầu trang"
    },
    "SHARE": {
      "social_media": "Chia sẻ",
      "file_sharing": "Gửi",
      "resource_sharing": "Đóng góp"
    }
  }
}
```

### 12.2 敏感话题处理

政治、宗教、文化等敏感话题的处理原则：

1. 避免使用政治色彩强烈的词汇
2. 宗教相关内容应保持中立，尊重用词
3. 文化特定内容需考虑越南本土文化背景

```json
{
  "SENSITIVE_TOPICS": {
    "religion": "采用中性词汇，避免偏向任何特定宗教",
    "politics": "使用官方认可的政治术语，保持客观",
    "traditional_customs": "尊重越南传统习俗，使用恰当词汇"
  }
}
```

### 12.3 礼貌程度词汇选择

根据目标受众和场合选择适当礼貌程度的词汇：

- 极其正式：官方文件，法律文本
- 正式：商业沟通，产品界面
- 中性：一般信息，指导说明
- 友好：社交媒体，促销内容
- 非正式：聊天对话，特定年轻群体

## 13. 常见翻译陷阱

### 13.1 易误译词组

容易误译的词组和表达：

```json
{
  "TRANSLATION_TRAPS": {
    "EXPRESSIONS": {
      "make sense": {
        "WRONG": "làm ý nghĩa",
        "CORRECT": "hợp lý / có lý"
      },
      "take place": {
        "WRONG": "lấy địa điểm",
        "CORRECT": "diễn ra / xảy ra"
      },
      "take time": {
        "WRONG": "lấy thời gian",
        "CORRECT": "mất thời gian"
      }
    }
  }
}
```

### 13.2 英语熟语的处理

英语熟语和惯用语的翻译策略：

1. 寻找越南语中对应的熟语表达
2. 如无对应表达，翻译其含义而非字面意思
3. 必要时可添加解释性内容

```json
{
  "IDIOMS": {
    "break the ice": {
      "LITERAL": "phá vỡ băng",
      "MEANING": "tạo không khí thoải mái / làm quen"
    },
    "cost an arm and a leg": {
      "LITERAL": "tốn một cánh tay và một chân",
      "MEANING": "cực kỳ đắt / rất tốn kém"
    }
  }
}
```

### 13.3 避免直译的陷阱

避免从英文直译到越南语的常见错误：

- 否定词位置问题：英语否定词通常在动词之前，越南语中可能在句子末尾
- 被动语态：越南语比英语更少使用被动语态
- 条件句：越南语条件句结构与英语不同

### 13.4 route 路由处理

遇到 "route" 变量时，后面跟着的路径不需要翻译。

参考例子：

```json
{
  "title": "帮助中心信息",
  "route": "new_portal_information",
  "body": "门户的基本信息",
  "CREATE_BASIC_SETTING_BUTTON": "创建门户基本设置"
},
```

## 14. 完整翻译示例

### 14.1 系统提示示例

**英文原文**：

```
Welcome to our platform! Please verify your email to complete registration. If you have any questions, our support team is here to help.
```

**越南语翻译（正式）**：

```
Chào mừng bạn đến với nền tảng của chúng tôi! Vui lòng xác minh email của bạn để hoàn tất đăng ký. Nếu bạn có bất kỳ câu hỏi nào, đội ngũ hỗ trợ của chúng tôi luôn sẵn sàng giúp đỡ.
```

**翻译要点说明**：

- 使用"nền tảng"（平台）而非保留英文"platform"
- 使用正式的"vui lòng"（请）表示礼貌请求
- 保持越南语中主语-动词-宾语的自然词序
- 使用"của chúng tôi"（我们的）表示所有关系

### 14.2 错误信息示例

**英文原文**：

```
Error: Unable to process your request. Please try again later or contact customer support if the problem persists.
```

**越南语翻译**：

```
Lỗi: Không thể xử lý yêu cầu của bạn. Vui lòng thử lại sau hoặc liên hệ bộ phận hỗ trợ khách hàng nếu sự cố vẫn tiếp diễn.
```

**翻译要点说明**：

- 技术术语"Error"翻译为"Lỗi"
- 使用"không thể"表示"unable to"（不能）
- 使用"sự cố"表示"problem"（问题/故障）
- 使用"tiếp diễn"表示"persists"（持续）的含义

### 14.3 对话示例

**英文对话**：

```
A: Hi there! How can I help you today?
B: I'm having trouble resetting my password. Can you guide me through the process?
A: Of course! I'd be happy to help. First, click on the "Forgot Password" link on the login page.
```

**越南语翻译**：

```
A: Xin chào! Tôi có thể giúp gì cho bạn hôm nay?
B: Tôi đang gặp khó khăn khi đặt lại mật khẩu. Bạn có thể hướng dẫn tôi quy trình không?
A: Tất nhiên rồi! Tôi rất vui được giúp đỡ bạn. Đầu tiên, hãy nhấp vào liên kết "Quên mật khẩu" trên trang đăng nhập.
```

**翻译要点说明**：

- 使用"Xin chào"作为友好的问候语
- 使用"đang gặp"表示进行时态"having"
- 使用恰当的技术术语："đặt lại mật khẩu"（重置密码）
- 保留UI元素的引号："Quên mật khẩu"
