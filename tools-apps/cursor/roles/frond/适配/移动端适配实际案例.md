<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-19 15:09:01
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-19 15:30:25
 * @FilePath     : /tools-apps/cursor/roles/frond/适配/移动端适配实际案例.md
 * @Description  : 移动端适配实际案例详解
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-19 15:09:01
-->

# 移动端适配实际案例详解

本文档提供四个完整的实际项目案例，展示如何在不同场景下综合应用移动端适配技术。每个案例都包含场景描述、技术要点和完整代码实现。

## 案例1：电商应用的三段式布局实现

### 场景描述

典型电商应用，顶部有搜索栏，中间是商品列表，底部有导航标签。需要处理的核心问题包括：

- 搜索栏在滚动时需要变为迷你模式，节省空间
- 商品列表支持下拉刷新和无限滚动
- 底部导航在商品详情页需要隐藏，返回时恢复
- 适配不同设备尺寸和方向变化

### 技术要点

1. **动态高度计算**：根据导航栏状态动态调整内容区域高度
2. **滚动事件处理**：监听滚动位置，实现搜索栏的迷你化
3. **路由状态检测**：根据当前页面类型决定是否显示底部导航
4. **屏幕旋转适配**：处理设备方向变化时的布局重新计算

### 代码实现

```javascript
class EcommerceLayoutManager {
  constructor() {
    this.navbar = document.querySelector('.search-navbar');
    this.productList = document.querySelector('.product-list');
    this.tabbar = document.querySelector('.nav-tabbar');

    this.lastScrollY = 0;
    this.scrollThreshold = 50;

    this.initLayout();
    this.bindEvents();
  }

  initLayout() {
    // 初始化三段式布局
    this.updateContentHeight();

    // 检测是否在商品详情页
    if (window.location.pathname.includes('/product/')) {
      this.tabbar.classList.add('hidden');
      this.updateContentHeight(true);
    }
  }

  updateContentHeight(noTabbar = false) {
    const navbarHeight = this.navbar.offsetHeight;
    const tabbarHeight = noTabbar ? 0 : this.tabbar.offsetHeight;

    this.productList.style.height =
      `calc(100vh - ${navbarHeight}px - ${tabbarHeight}px)`;
  }

  bindEvents() {
    // 处理滚动事件，实现搜索栏迷你化
    this.productList.addEventListener('scroll', () => {
      const scrollY = this.productList.scrollTop;

      if (scrollY > this.scrollThreshold && this.lastScrollY <= this.scrollThreshold) {
        this.navbar.classList.add('mini-mode');
        this.updateContentHeight();
      } else if (scrollY <= this.scrollThreshold && this.lastScrollY > this.scrollThreshold) {
        this.navbar.classList.remove('mini-mode');
        this.updateContentHeight();
      }

      this.lastScrollY = scrollY;
    });

    // 处理屏幕旋转
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.updateContentHeight(), 300);
    });
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new EcommerceLayoutManager();
});
```

### 相关CSS

```css
.search-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;
  transition: height 0.3s ease;
  z-index: 100;
  padding-top: env(safe-area-inset-top);
}

.search-navbar.mini-mode {
  height: 44px;
}

.product-list {
  position: fixed;
  top: 56px;
  left: 0;
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.nav-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  padding-bottom: env(safe-area-inset-bottom);
  transition: transform 0.3s ease;
  z-index: 100;
}

.nav-tabbar.hidden {
  transform: translateY(100%);
}
```

## 案例2：视频播放应用的沉浸式体验

### 场景描述

视频播放应用，需要在播放时提供沉浸式体验，暂停时显示控制界面。核心需求包括：

- 播放时自动隐藏导航栏和控制栏，提供沉浸式体验
- 点击屏幕可以切换控制界面的显示/隐藏
- 横屏模式下实现全屏沉浸式播放
- 支持画中画模式和系统手势

### 技术要点

1. **全屏模式处理**：在不同状态下调整视频播放器尺寸和位置
2. **控制界面切换**：根据用户交互和播放状态显示/隐藏控制界面
3. **屏幕方向检测**：识别设备方向并调整布局
4. **系统手势兼容**：确保应用控制不与系统手势冲突

### 代码实现

```javascript
class VideoPlayerLayoutManager {
  constructor() {
    this.player = document.querySelector('.video-player');
    this.controls = document.querySelector('.player-controls');
    this.navbar = document.querySelector('.video-navbar');
    this.isPlaying = false;
    this.controlsVisible = true;
    this.isFullscreen = false;

    this.initLayout();
    this.bindEvents();
  }

  initLayout() {
    // 检测初始方向
    this.handleOrientationChange();

    // 初始化播放器尺寸
    this.updatePlayerSize();
  }

  updatePlayerSize() {
    if (this.isFullscreen) {
      // 全屏模式
      this.player.style.height = '100vh';
      this.player.style.width = '100vw';
      document.body.classList.add('video-fullscreen');
    } else {
      // 常规模式
      const navbarHeight = this.controlsVisible ? this.navbar.offsetHeight : 0;
      this.player.style.height = `calc(56.25vw + ${navbarHeight}px)`;
      this.player.style.width = '100%';
      document.body.classList.remove('video-fullscreen');
    }
  }

  toggleControls() {
    this.controlsVisible = !this.controlsVisible;

    if (this.controlsVisible) {
      this.controls.classList.remove('hidden');
      this.navbar.classList.remove('hidden');
    } else {
      this.controls.classList.add('hidden');
      this.navbar.classList.add('hidden');
    }

    this.updatePlayerSize();
  }

  handleOrientationChange() {
    const isLandscape = window.matchMedia('(orientation: landscape)').matches;

    if (isLandscape && this.isPlaying) {
      // 横屏播放时自动全屏
      this.isFullscreen = true;
      this.controlsVisible = false;
    } else if (!isLandscape) {
      // 竖屏时退出全屏
      this.isFullscreen = false;
      this.controlsVisible = true;
    }

    this.updatePlayerSize();
  }

  bindEvents() {
    // 点击视频区域切换控制显示
    this.player.addEventListener('click', () => {
      if (this.isPlaying) {
        this.toggleControls();
      }
    });

    // 监听播放状态
    this.player.addEventListener('play', () => {
      this.isPlaying = true;
      // 播放3秒后自动隐藏控制
      setTimeout(() => {
        if (this.isPlaying) this.toggleControls();
      }, 3000);
    });

    this.player.addEventListener('pause', () => {
      this.isPlaying = false;
      this.controlsVisible = true;
      this.controls.classList.remove('hidden');
      this.navbar.classList.remove('hidden');
    });

    // 监听屏幕旋转
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.handleOrientationChange(), 300);
    });

    // 处理画中画模式
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && this.isPlaying) {
        // 从画中画返回时恢复界面
        this.handleOrientationChange();
      }
    });
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new VideoPlayerLayoutManager();
});
```

### 相关CSS

```css
body.video-fullscreen {
  overflow: hidden;
}

.video-player {
  position: relative;
  background: #000;
  transition: all 0.3s ease;
}

.video-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 44px;
  padding-top: env(safe-area-inset-top);
  transition: opacity 0.3s ease;
  z-index: 200;
}

.player-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 44px;
  padding-bottom: env(safe-area-inset-bottom);
  transition: opacity 0.3s ease;
  z-index: 100;
}

.hidden {
  opacity: 0;
  pointer-events: none;
}
```

## 案例3：社交媒体应用的复杂交互布局

### 场景描述

社交媒体应用，包含信息流、评论区、发布功能等复杂交互场景。主要挑战包括：

- 信息流支持下拉刷新和无限滚动
- 评论区弹出时的键盘适配
- 发布界面的全屏模式与正常模式切换
- 多级导航的处理（主导航+二级导航）

### 技术要点

1. **键盘处理**：检测键盘弹出并调整布局
2. **模态框定位**：确保评论区和发布界面在不同场景下的正确定位
3. **浮动按钮适配**：处理发布按钮在不同设备和方向下的位置
4. **多级导航协调**：处理主导航和二级导航的协同工作

### 代码实现

```javascript
class SocialAppLayoutManager {
  constructor() {
    this.navbar = document.querySelector('.main-navbar');
    this.tabbar = document.querySelector('.main-tabbar');
    this.feedContainer = document.querySelector('.feed-container');
    this.commentModal = document.querySelector('.comment-modal');
    this.composeButton = document.querySelector('.compose-button');
    this.composeModal = document.querySelector('.compose-modal');

    this.originalViewportHeight = window.innerHeight;
    this.isKeyboardVisible = false;

    this.initLayout();
    this.bindEvents();
  }

  initLayout() {
    this.updateContentHeight();

    // 设置发布按钮位置
    this.updateComposeButtonPosition();
  }

  updateContentHeight() {
    const navbarHeight = this.navbar.offsetHeight;
    const tabbarHeight = this.tabbar.offsetHeight;

    this.feedContainer.style.height =
      `calc(100vh - ${navbarHeight}px - ${tabbarHeight}px)`;
    this.feedContainer.style.top = `${navbarHeight}px`;
  }

  updateComposeButtonPosition() {
    // 浮动按钮定位在TabBar上方
    const safeBottom = getComputedStyle(document.documentElement)
      .getPropertyValue('env(safe-area-inset-bottom)') || '0px';

    this.composeButton.style.bottom =
      `calc(${this.tabbar.offsetHeight}px + ${safeBottom} + 16px)`;
  }

  handleKeyboardVisibility() {
    const currentHeight = window.innerHeight;

    // 检测键盘是否弹出
    if (currentHeight < this.originalViewportHeight * 0.8) {
      if (!this.isKeyboardVisible) {
        this.isKeyboardVisible = true;

        // 键盘弹出时隐藏TabBar
        this.tabbar.classList.add('hidden');

        // 调整评论框位置
        this.commentModal.classList.add('keyboard-visible');
      }
    } else {
      if (this.isKeyboardVisible) {
        this.isKeyboardVisible = false;

        // 键盘收起时恢复TabBar
        this.tabbar.classList.remove('hidden');

        // 恢复评论框位置
        this.commentModal.classList.remove('keyboard-visible');
      }
    }
  }

  openComposeModal() {
    // 打开发布界面
    this.composeModal.classList.add('active');

    // 隐藏主导航和TabBar
    this.navbar.classList.add('hidden');
    this.tabbar.classList.add('hidden');

    // 全屏模式
    document.body.classList.add('compose-mode');
  }

  closeComposeModal() {
    // 关闭发布界面
    this.composeModal.classList.remove('active');

    // 恢复主导航和TabBar
    this.navbar.classList.remove('hidden');
    this.tabbar.classList.remove('hidden');

    // 退出全屏模式
    document.body.classList.remove('compose-mode');
  }

  bindEvents() {
    // 监听窗口大小变化，检测键盘
    window.addEventListener('resize', () => {
      this.handleKeyboardVisibility();
      this.updateContentHeight();
    });

    // 发布按钮点击
    this.composeButton.addEventListener('click', () => {
      this.openComposeModal();
    });

    // 关闭发布界面
    document.querySelector('.compose-close').addEventListener('click', () => {
      this.closeComposeModal();
    });

    // 屏幕旋转
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.updateContentHeight();
        this.updateComposeButtonPosition();
      }, 300);
    });
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new SocialAppLayoutManager();
});
```

### 相关CSS

```css
.main-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 44px;
  padding-top: env(safe-area-inset-top);
  z-index: 100;
  transition: transform 0.3s ease;
}

.main-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 100;
  transition: transform 0.3s ease;
}

.feed-container {
  position: fixed;
  overflow-y: auto;
  width: 100%;
  -webkit-overflow-scrolling: touch;
}

.compose-button {
  position: fixed;
  right: 16px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  z-index: 90;
}

.comment-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 200;
}

.comment-modal.active {
  transform: translateY(0);
}

.comment-modal.keyboard-visible {
  border-radius: 0;
}

.compose-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 300;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.compose-modal.active {
  transform: translateY(0);
}

.hidden {
  transform: translateY(100%);
}

body.compose-mode {
  overflow: hidden;
}
```

## 案例4：PWA应用与浏览器模式的双重适配

### 场景描述

一个同时支持PWA安装和普通浏览器访问的应用，需要在两种模式下提供一致的用户体验。主要挑战包括：

- 自动检测PWA模式与浏览器模式
- 两种模式下的不同适配策略
- 状态栏颜色和样式适配
- 安全区域处理

### 技术要点

1. **模式检测**：准确识别当前是PWA模式还是浏览器模式
2. **条件适配**：根据不同模式应用不同的布局策略
3. **状态栏处理**：在PWA模式下正确设置状态栏颜色和样式
4. **动态监听**：监听显示模式变化，实时调整布局

### 代码实现

```javascript
class DualModeLayoutManager {
  constructor() {
    this.isPWA = this.detectPWAMode();
    this.navbar = document.querySelector('.app-navbar');
    this.content = document.querySelector('.app-content');
    this.tabbar = document.querySelector('.app-tabbar');

    this.initLayout();
    this.bindEvents();
  }

  detectPWAMode() {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone ||
           document.referrer.includes('android-app://');
  }

  initLayout() {
    // 根据模式应用不同类名
    document.documentElement.classList.add(
      this.isPWA ? 'pwa-mode' : 'browser-mode'
    );

    // 设置状态栏颜色（PWA模式）
    if (this.isPWA) {
      // 设置状态栏颜色
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', '#ffffff');
      }

      // 添加安全区域padding
      this.navbar.classList.add('pwa-navbar');
      this.tabbar.classList.add('pwa-tabbar');
    } else {
      // 浏览器模式下监听UI变化
      this.setupBrowserUIListener();
    }

    // 更新内容区域高度
    this.updateContentHeight();
  }

  setupBrowserUIListener() {
    let lastHeight = window.innerHeight;

    window.addEventListener('scroll', () => {
      // 监测浏览器UI变化
      if (window.innerHeight !== lastHeight) {
        this.updateContentHeight();
        lastHeight = window.innerHeight;
      }
    });
  }

  updateContentHeight() {
    const navbarHeight = this.navbar.offsetHeight;
    const tabbarHeight = this.tabbar.offsetHeight;

    if (this.isPWA) {
      // PWA模式：使用安全区域变量
      this.content.style.height = `
        calc(100vh - ${navbarHeight}px - ${tabbarHeight}px)
      `;
    } else {
      // 浏览器模式：使用动态计算的高度
      this.content.style.height = `
        calc(${window.innerHeight}px - ${navbarHeight}px - ${tabbarHeight}px)
      `;
    }
  }

  bindEvents() {
    // 监听显示模式变化
    window.matchMedia('(display-mode: standalone)').addEventListener('change', (e) => {
      this.isPWA = e.matches;
      document.documentElement.classList.toggle('pwa-mode', this.isPWA);
      document.documentElement.classList.toggle('browser-mode', !this.isPWA);
      this.updateContentHeight();
    });

    // 监听屏幕旋转
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.updateContentHeight(), 300);
    });

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.updateContentHeight();
    });
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  new DualModeLayoutManager();
});
```

### 相关CSS

```css
:root {
  --navbar-height: 44px;
  --tabbar-height: 50px;
}

.app-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--navbar-height);
  z-index: 100;
}

.app-navbar.pwa-navbar {
  padding-top: env(safe-area-inset-top);
}

.app-content {
  position: fixed;
  top: var(--navbar-height);
  left: 0;
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.app-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: var(--tabbar-height);
  z-index: 100;
}

.app-tabbar.pwa-tabbar {
  padding-bottom: env(safe-area-inset-bottom);
}

/* PWA模式特有样式 */
.pwa-mode .app-navbar {
  background-color: #ffffff;
}

/* 浏览器模式特有样式 */
.browser-mode .app-navbar {
  background-color: #f8f8f8;
}
```

## 总结与最佳实践

通过以上四个实际案例，我们可以总结出以下移动端适配的最佳实践：

1. **结构化的布局管理**：使用类来封装布局逻辑，便于维护和扩展
2. **动态高度计算**：不依赖固定值，而是根据实际元素高度和环境变量计算
3. **事件优化**：使用防抖和延时处理频繁触发的事件，如resize和scroll
4. **模式检测**：准确检测当前运行环境，应用相应的适配策略
5. **安全区域处理**：正确使用CSS环境变量处理不同设备的安全区域
6. **键盘适配**：检测键盘状态并相应调整布局
7. **过渡动画**：添加适当的过渡效果，避免布局突变
8. **性能考虑**：避免频繁DOM操作，合理使用requestAnimationFrame

这些案例和最佳实践可以作为移动端Web应用开发的参考，根据具体项目需求进行调整和扩展。

## 常见问题和解决方案

在移动端适配过程中，开发者经常会遇到一些棘手的问题。以下是最常见的问题及其解决方案：

### 1. 100vh 高度计算错误

**问题**：在移动浏览器中，`100vh` 通常包含了浏览器UI（地址栏、工具栏）的高度，导致内容溢出。

**解决方案**：

```javascript
// 方案1：使用动态视口单位
.container {
  height: 100dvh; /* 动态视口高度，自动适应浏览器UI */
}

// 方案2：使用JavaScript计算实际高度
function setRealVh() {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}

window.addEventListener('resize', setRealVh);
setRealVh();

// 在CSS中使用
.container {
  height: calc(var(--vh, 1vh) * 100);
}
```

### 2. 键盘弹出导致布局错乱

**问题**：虚拟键盘弹出时会改变视口高度，导致布局错乱或输入框被键盘遮挡。

**解决方案**：

```javascript
const originalHeight = window.innerHeight;

window.addEventListener('resize', () => {
  // 键盘弹出检测
  if (window.innerHeight < originalHeight * 0.8) {
    // 键盘弹出
    document.body.classList.add('keyboard-open');
    // 滚动到输入框
    document.activeElement.scrollIntoView({behavior: 'smooth'});
  } else {
    // 键盘收起
    document.body.classList.remove('keyboard-open');
  }
});
```

### 3. 安全区域适配问题

**问题**：刘海屏、底部Home指示器等特殊区域导致内容被遮挡。

**解决方案**：

```css
/* 适配安全区域 */
.safe-top {
  padding-top: env(safe-area-inset-top);
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 兼容性处理 */
@supports not (padding: env(safe-area-inset-top)) {
  .safe-top {
    padding-top: 20px; /* 降级方案 */
  }

  .safe-bottom {
    padding-bottom: 20px; /* 降级方案 */
  }
}
```

### 4. 滚动穿透问题

**问题**：模态框打开时，背景内容仍可滚动。

**解决方案**：

```javascript
// 打开模态框时
function openModal() {
  // 记录滚动位置
  const scrollY = window.scrollY;

  // 固定body
  document.body.style.position = 'fixed';
  document.body.style.top = `-${scrollY}px`;
  document.body.style.width = '100%';
  document.body.dataset.scrollY = scrollY;
}

// 关闭模态框时
function closeModal() {
  // 恢复滚动位置
  const scrollY = parseInt(document.body.dataset.scrollY || '0');

  document.body.style.position = '';
  document.body.style.top = '';
  document.body.style.width = '';

  window.scrollTo(0, scrollY);
}
```

### 5. 1像素边框问题

**问题**：在高DPR设备上，1px的CSS边框实际显示为多像素，看起来过粗。

**解决方案**：

```css
/* 方案1：使用transform缩放 */
.border-1px {
  position: relative;
}

.border-1px::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: #000;
  transform: scaleY(0.5);
  transform-origin: 0 100%;
}

/* 方案2：使用渐变 */
.border-gradient {
  border: 0;
  background-image: linear-gradient(to bottom, transparent, transparent 50%, #000 50%, #000 100%);
  background-size: 100% 1px;
  background-repeat: no-repeat;
  background-position: bottom;
}
```

### 6. 浏览器兼容性差异

**问题**：不同浏览器对CSS特性支持不一致，导致布局差异。

**解决方案**：

```css
/* 使用@supports检测特性支持 */
.container {
  display: flex;
}

@supports not (display: flex) {
  .container {
    display: block;
  }
}

/* 或使用Modernizr等库检测 */
if (Modernizr.flexbox) {
  // 支持flexbox
} else {
  // 降级方案
}
```

### 7. 滚动条样式不一致

**问题**：不同平台的滚动条样式差异大，影响一致性体验。

**解决方案**：

```css
/* 自定义滚动条样式 */
.custom-scroll {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 仅WebKit浏览器 */
.custom-scroll::-webkit-scrollbar {
  width: 4px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 隐藏滚动条但保留功能 */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE/Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}
```

### 8. 图片自适应问题

**问题**：图片在不同尺寸设备上的展示比例不一致。

**解决方案**：

```css
/* 方案1：使用object-fit */
.responsive-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

/* 方案2：使用aspect-ratio */
.aspect-container {
  width: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
}

/* 方案3：使用padding-top技巧 */
.aspect-box {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9比例 */
}

.aspect-box img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
```

## 性能优化专题

移动端Web应用的性能优化至关重要，直接影响用户体验和留存率。以下是关键的性能优化策略：

### 1. 避免布局抖动（Layout Thrashing）

布局抖动是性能杀手，发生在JS频繁读取和修改DOM导致浏览器反复重排重绘。

**优化策略**：

```javascript
// 不好的做法 - 导致多次重排
function badLayout() {
  const box = document.querySelector('.box');

  box.style.width = '100px'; // 写操作，触发重排
  console.log(box.offsetWidth); // 读操作，强制重排
  box.style.height = '100px'; // 写操作，再次触发重排
  console.log(box.offsetHeight); // 读操作，再次强制重排
}

// 好的做法 - 批量读写分离
function goodLayout() {
  const box = document.querySelector('.box');

  // 先读取
  const width = box.offsetWidth;
  const height = box.offsetHeight;

  // 再写入
  box.style.width = '100px';
  box.style.height = '100px';
}

// 更好的做法 - 使用requestAnimationFrame
function betterLayout() {
  // 读取操作
  const reads = [];
  const elements = document.querySelectorAll('.box');

  elements.forEach(el => {
    reads.push({
      el,
      width: el.offsetWidth,
      height: el.offsetHeight
    });
  });

  // 写入操作
  requestAnimationFrame(() => {
    reads.forEach(item => {
      item.el.style.width = `${item.width + 10}px`;
      item.el.style.height = `${item.height + 10}px`;
    });
  });
}
```

### 2. 使用CSS硬件加速

利用GPU加速可以显著提升动画和滚动性能。

**优化策略**：

```css
/* 启用硬件加速的CSS属性 */
.hardware-accelerated {
  transform: translateZ(0); /* 或 translate3d(0,0,0) */
  will-change: transform; /* 提示浏览器元素将发生变化 */
  backface-visibility: hidden;
}

/* 注意：过度使用will-change会适得其反，仅在必要时使用 */
.scrollable-content {
  will-change: transform; /* 用于滚动容器 */
}
```

### 3. 防抖和节流处理

对于频繁触发的事件（如resize、scroll、input），应用防抖和节流技术。

**优化策略**：

```javascript
// 防抖函数 - 等待停止操作后执行
function debounce(fn, delay) {
  let timer = null;
  return function() {
    const context = this;
    const args = arguments;
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
}

// 节流函数 - 按时间间隔执行
function throttle(fn, interval) {
  let lastTime = 0;
  return function() {
    const context = this;
    const args = arguments;
    const now = Date.now();

    if (now - lastTime >= interval) {
      lastTime = now;
      fn.apply(context, args);
    }
  };
}

// 应用
window.addEventListener('resize', debounce(() => {
  updateLayout();
}, 200));

window.addEventListener('scroll', throttle(() => {
  checkVisibility();
}, 100));
```

### 4. 虚拟列表优化

对于长列表，使用虚拟列表技术只渲染可视区域内的元素。

**优化策略**：

```javascript
class VirtualList {
  constructor(container, itemHeight, totalItems, renderItem) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.totalItems = totalItems;
    this.renderItem = renderItem;

    this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + 5; // 缓冲区
    this.startIndex = 0;
    this.endIndex = this.visibleItems;

    this.init();
  }

  init() {
    // 设置容器高度
    const totalHeight = this.totalItems * this.itemHeight;
    this.container.style.height = `${totalHeight}px`;

    // 创建滚动容器
    this.scrollContainer = document.createElement('div');
    this.scrollContainer.style.position = 'relative';
    this.container.appendChild(this.scrollContainer);

    // 监听滚动
    this.container.addEventListener('scroll', this.handleScroll.bind(this));

    // 初始渲染
    this.renderItems();
  }

  handleScroll() {
    const scrollTop = this.container.scrollTop;
    const newStartIndex = Math.floor(scrollTop / this.itemHeight);

    if (newStartIndex !== this.startIndex) {
      this.startIndex = newStartIndex;
      this.endIndex = newStartIndex + this.visibleItems;
      this.renderItems();
    }
  }

  renderItems() {
    // 清空当前内容
    this.scrollContainer.innerHTML = '';

    // 只渲染可见区域的项目
    for (let i = this.startIndex; i < Math.min(this.endIndex, this.totalItems); i++) {
      const item = this.renderItem(i);
      item.style.position = 'absolute';
      item.style.top = `${i * this.itemHeight}px`;
      item.style.height = `${this.itemHeight}px`;
      item.style.width = '100%';
      this.scrollContainer.appendChild(item);
    }
  }
}

// 使用示例
const container = document.querySelector('.list-container');
const virtualList = new VirtualList(
  container,
  50, // 每项高度
  10000, // 总项目数
  (index) => {
    const div = document.createElement('div');
    div.textContent = `Item ${index}`;
    return div;
  }
);
```

### 5. 图片优化

图片通常是移动端性能瓶颈，需要特别优化。

**优化策略**：

```html
<!-- 使用响应式图片 -->
<picture>
  <source media="(max-width: 600px)" srcset="small.webp">
  <source media="(max-width: 1200px)" srcset="medium.webp">
  <img src="large.webp" alt="响应式图片">
</picture>

<!-- 使用WebP格式 -->
<picture>
  <source type="image/webp" srcset="image.webp">
  <source type="image/jpeg" srcset="image.jpg">
  <img src="image.jpg" alt="WebP回退">
</picture>

<!-- 延迟加载 -->
<img src="placeholder.jpg" data-src="actual-image.jpg" class="lazy" alt="延迟加载">
```

```javascript
// 实现延迟加载
function lazyLoad() {
  const lazyImages = document.querySelectorAll('img.lazy');
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        imageObserver.unobserve(img);
      }
    });
  });

  lazyImages.forEach(img => {
    imageObserver.observe(img);
  });
}

// 兼容性处理
if ('IntersectionObserver' in window) {
  document.addEventListener('DOMContentLoaded', lazyLoad);
} else {
  // 降级方案
  // ...
}
```

### 6. CSS性能优化

CSS选择器和属性也会影响渲染性能。

**优化策略**：

```css
/* 避免使用昂贵的属性 */
.expensive {
  box-shadow: 0 0 10px rgba(0,0,0,0.5); /* 昂贵 */
  filter: blur(5px); /* 昂贵 */
  opacity: 0.9; /* 相对昂贵 */
  transform: skew(10deg); /* 相对昂贵 */
}

/* 使用简单选择器 */
/* 不好的选择器 */
body div.container ul li a.link { /* 复杂，昂贵 */
  color: red;
}

/* 好的选择器 */
.link { /* 简单，高效 */
  color: red;
}

/* 避免@import，它会阻塞渲染 */
/* 不好的做法 */
@import url('other.css');

/* 好的做法 - 在HTML中使用多个link标签 */
```

## 调试工具和技巧

移动端适配调试可能比桌面端更复杂，以下是一些实用的调试工具和技巧：

### 1. 浏览器开发者工具

#### Chrome DevTools

**设备模式**：

- 打开DevTools (F12) → 点击"Toggle Device Toolbar" (Ctrl+Shift+M)
- 可以选择预设设备或自定义尺寸
- 可以模拟不同的DPR、网络条件和方向

**关键技巧**：

- 使用"Responsive"模式拖动调整尺寸
- 使用"Show media queries"可视化断点
- 使用"Show rulers"查看精确尺寸

```javascript
// 在控制台中模拟触摸事件
const touchEvent = new TouchEvent('touchstart', {
  touches: [{
    identifier: 0,
    target: document.querySelector('.button'),
    clientX: 100,
    clientY: 100
  }]
});

document.querySelector('.button').dispatchEvent(touchEvent);
```

#### Safari Web Inspector

对于iOS设备调试必不可少：

1. 在iOS设备上：设置 → Safari → 高级 → Web检查器
2. 在Mac上：Safari → 偏好设置 → 高级 → 显示开发菜单
3. 连接设备，在Safari的开发菜单中选择设备和页面

### 2. 远程调试工具

#### Eruda - 移动端控制台

在移动设备上直接调试的轻量级工具：

```html
<script src="//cdn.jsdelivr.net/npm/eruda"></script>
<script>eruda.init();</script>
```

主要功能：

- 控制台输出
- 元素审查
- 网络请求监控
- 资源查看
- 性能分析

#### Weinre/VConsole

类似Eruda的替代方案：

```html
<!-- VConsole -->
<script src="//unpkg.com/vconsole/dist/vconsole.min.js"></script>
<script>
  var vConsole = new VConsole();
</script>
```

### 3. 真机测试服务

#### BrowserStack

提供真实设备的远程访问：

- 支持数千种设备和浏览器组合
- 提供实时交互式测试
- 支持本地隧道测试内部网站

#### Sauce Labs

类似BrowserStack，提供自动化测试功能：

- 支持自动化测试脚本
- 提供视频录制和截图
- 集成CI/CD流程

### 4. 实用调试技巧

#### 可视化调试

```css
/* 为不同元素添加边框以便调试 */
.debug * {
  outline: 1px solid red;
}

.debug .container {
  outline: 2px solid blue;
}

.debug .content {
  outline: 2px solid green;
}
```

#### 调试视口和安全区域

```css
/* 显示视口边界 */
body::after {
  content: 'Viewport';
  position: fixed;
  bottom: 0;
  right: 0;
  background: rgba(0,0,0,0.5);
  color: white;
  padding: 5px;
  font-size: 10px;
  z-index: 10000;
}

/* 显示安全区域 */
.safe-area-debug {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9999;
}

.safe-area-debug::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: env(safe-area-inset-top);
  background: rgba(255,0,0,0.3);
}

.safe-area-debug::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: env(safe-area-inset-bottom);
  background: rgba(0,0,255,0.3);
}
```

#### 动态信息显示

```javascript
// 创建调试面板
function createDebugPanel() {
  const panel = document.createElement('div');
  panel.style.cssText = `
    position: fixed;
    bottom: 0;
    left: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 10px;
    font-size: 12px;
    font-family: monospace;
    z-index: 10000;
    max-width: 100%;
    overflow: auto;
  `;
  document.body.appendChild(panel);

  // 更新信息
  function updateInfo() {
    panel.innerHTML = `
      <div>Window: ${window.innerWidth} x ${window.innerHeight}</div>
      <div>Screen: ${window.screen.width} x ${window.screen.height}</div>
      <div>Pixel Ratio: ${window.devicePixelRatio}</div>
      <div>Orientation: ${screen.orientation ? screen.orientation.type : 'N/A'}</div>
      <div>Safe Area Top: ${getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-top)')}</div>
      <div>Safe Area Bottom: ${getComputedStyle(document.documentElement).getPropertyValue('env(safe-area-inset-bottom)')}</div>
      <div>User Agent: ${navigator.userAgent.substring(0, 50)}...</div>
    `;
  }

  window.addEventListener('resize', updateInfo);
  updateInfo();

  return panel;
}

// 使用
const debugPanel = createDebugPanel();
```

### 5. 性能分析工具

#### Lighthouse

Chrome内置的性能审计工具：

- 打开DevTools → Lighthouse标签
- 可分析性能、可访问性、最佳实践等
- 提供具体优化建议

#### Performance Monitor

实时监控性能指标：

- 打开DevTools → 按Esc → 选择"Performance Monitor"
- 监控CPU使用率、DOM节点数、JS堆大小等

#### WebPageTest

更详细的性能分析：

- 支持多地点测试
- 提供瀑布图、视频和性能指标
- 支持比较不同版本

### 6. 移动端特有调试技巧

#### 触摸事件调试

```javascript
// 可视化触摸点
function visualizeTouches() {
  const touchIndicators = [];

  function createTouchIndicator(id) {
    const indicator = document.createElement('div');
    indicator.style.cssText = `
      position: absolute;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: rgba(255,0,0,0.5);
      pointer-events: none;
      transform: translate(-50%, -50%);
      z-index: 10000;
    `;
    document.body.appendChild(indicator);
    return indicator;
  }

  document.addEventListener('touchstart', (e) => {
    e.preventDefault();
    Array.from(e.touches).forEach(touch => {
      if (!touchIndicators[touch.identifier]) {
        touchIndicators[touch.identifier] = createTouchIndicator(touch.identifier);
      }

      const indicator = touchIndicators[touch.identifier];
      indicator.style.left = `${touch.clientX}px`;
      indicator.style.top = `${touch.clientY}px`;
    });
  }, {passive: false});

  document.addEventListener('touchmove', (e) => {
    Array.from(e.touches).forEach(touch => {
      if (touchIndicators[touch.identifier]) {
        const indicator = touchIndicators[touch.identifier];
        indicator.style.left = `${touch.clientX}px`;
        indicator.style.top = `${touch.clientY}px`;
      }
    });
  }, {passive: true});

  document.addEventListener('touchend', (e) => {
    Array.from(e.changedTouches).forEach(touch => {
      if (touchIndicators[touch.identifier]) {
        document.body.removeChild(touchIndicators[touch.identifier]);
        delete touchIndicators[touch.identifier];
      }
    });
  }, {passive: true});
}

// 使用
visualizeTouches();
```

#### 设备方向调试

```javascript
// 监控设备方向变化
function monitorOrientation() {
  const info = document.createElement('div');
  info.style.cssText = `
    position: fixed;
    top: 0;
    right: 0;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px;
    font-size: 12px;
    z-index: 10000;
  `;
  document.body.appendChild(info);

  function updateOrientation() {
    const orientation = screen.orientation ? screen.orientation.type :
                       (window.orientation === 0 || window.orientation === 180 ? 'portrait' : 'landscape');

    info.textContent = `Orientation: ${orientation}`;
    info.style.background = orientation.includes('portrait') ? 'rgba(0,0,255,0.7)' : 'rgba(255,0,0,0.7)';
  }

  window.addEventListener('orientationchange', updateOrientation);
  window.addEventListener('resize', updateOrientation);
  updateOrientation();
}

// 使用
monitorOrientation();
```

这些工具和技巧可以帮助开发者更有效地调试和优化移动端Web应用，提高开发效率和应用质量。
