<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-19 12:41:14
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-07-19 12:41:18
 * @FilePath     : /tools-apps/cursor/roles/frond/适配.md
 * @Description  : 移动端内容区域适配方案详解
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-19 12:41:14
-->

# 移动端内容区域适配方案详解

## 核心思路

在移动端 Web 应用中，内容区域的适配是一个常见挑战，特别是在不同环境（浏览器/PWA应用）下保持一致的用户体验。

**基本适配思路：**
1. 给主容器（如 `#app`）设置精确计算的高度
2. 从可用视口高度中减去所有固定元素和安全区域  
3. 设置主容器 `overflow: hidden`
4. 子页面设置 `height: 100%` 和适当的滚动属性

这个方案能够同时适配浏览器环境和 PWA 应用环境，无论环境如何变化，内容区域都能保持正确的高度和滚动行为。

## 基础计算逻辑

```css
#app {
  height: calc(
    100vh 
    - var(--navbar-height, 0px)     /* 导航栏高度 */
    - var(--tabbar-height, 0px)     /* 底部标签栏高度 */
    - env(safe-area-inset-top)      /* 顶部安全区域（已包含状态栏） */
    - env(safe-area-inset-bottom)   /* 底部安全区域 */
  );
  overflow: hidden;
}

.page {
  height: 100%;
  overflow: auto;
}
```

## 优化后的完整方案

### 1. CSS 变量定义

```css
:root {
  --navbar-height: 60px;
  --tabbar-height: 60px;
  --actual-vh: 100vh;
}
```

### 2. 动态视口高度适配

```css
#app {
  /* 使用动态视口单位，自动适配地址栏变化 */
  height: calc(
    100dvh  /* 或使用 var(--actual-vh) 通过 JS 动态设置 */
    - var(--navbar-height)
    - var(--tabbar-height)
    - env(safe-area-inset-top)
    - env(safe-area-inset-bottom)
  );
  overflow: hidden;
  position: relative;
}

.page {
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch; /* iOS 滚动优化 */
  overscroll-behavior: contain; /* 防止滚动传播 */
}
```

### 3. 兼容性回退

```css
/* 兼容不支持 env() 的浏览器 */
@supports not (height: env(safe-area-inset-top)) {
  #app {
    height: calc(var(--actual-vh) - var(--navbar-height) - var(--tabbar-height));
  }
}
```

## JavaScript 动态适配

### 1. 处理虚拟键盘和视口变化

```javascript
// 监听视口变化，处理虚拟键盘和地址栏变化
function handleViewportChange() {
  const vh = window.visualViewport?.height || window.innerHeight;
  document.documentElement.style.setProperty('--actual-vh', `${vh}px`);
}

// 初始化
handleViewportChange();

// 监听变化
window.visualViewport?.addEventListener('resize', handleViewportChange);
window.addEventListener('resize', handleViewportChange);
```

### 2. 虚拟键盘专门处理

虚拟键盘弹出时会改变视口高度，需要特殊处理：

```javascript
const originalHeight = window.innerHeight;

function handleKeyboardToggle() {
  const currentHeight = window.innerHeight;
  
  if (currentHeight < originalHeight * 0.8) {
    // 键盘可能弹出，调整布局
    document.documentElement.style.setProperty('--is-keyboard-open', 'true');
    document.body.classList.add('keyboard-open');
  } else {
    // 键盘收起
    document.documentElement.style.setProperty('--is-keyboard-open', 'false');
    document.body.classList.remove('keyboard-open');
  }
}

window.addEventListener('resize', handleKeyboardToggle);
```

```css
/* 键盘打开时的样式调整 */
.keyboard-open .should-adjust-for-keyboard {
  bottom: 0;
  transform: translateY(0);
}

:root[style*="--is-keyboard-open: true"] .floating-input {
  position: fixed;
  bottom: 10px;
}
```

### 3. 设备检测和动态调整

```javascript
// 设备检测工具
const DeviceAdapter = {
  // 检测是否为移动设备
  isMobile() {
    return /Android|iPhone|iPad/i.test(navigator.userAgent);
  },

  // 检测是否为 PWA 环境
  isPWA() {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone === true;
  },

  // 动态设置导航栏高度
  setNavbarHeight(height) {
    document.documentElement.style.setProperty('--navbar-height', `${height}px`);
  },

  // 动态设置底部标签栏高度
  setTabbarHeight(height) {
    document.documentElement.style.setProperty('--tabbar-height', `${height}px`);
  }
};
```

## 关键注意事项

### 1. 状态栏高度处理

❌ **错误做法**：单独减去状态栏高度
```css
/* 不需要这样做 */
height: calc(100vh - var(--status-bar-height) - env(safe-area-inset-top));
```

✅ **正确做法**：`env(safe-area-inset-top)` 已包含状态栏高度
```css
height: calc(100vh - env(safe-area-inset-top));
```

### 2. 动态视口单位选择

```css
/* 传统方案 */
height: 100vh;  /* 固定视口高度，不适配地址栏变化 */

/* 现代方案 */
height: 100dvh; /* 动态视口高度，自动适配地址栏 */
height: 100svh; /* 小视口高度，地址栏显示时的高度 */
height: 100lvh; /* 大视口高度，地址栏隐藏时的高度 */
```

### 3. 滚动性能优化

```css
.page {
  height: 100%;
  overflow: auto;
  
  /* iOS 滚动优化 */
  -webkit-overflow-scrolling: touch;
  
  /* 防止过度滚动 */
  overscroll-behavior: contain;
  
  /* 滚动条样式 */
  scrollbar-width: thin;
}
```

### 4. iOS 特有问题处理

iOS Safari 有"弹性滚动"效果，可能需要额外处理：

```css
html, body {
  overscroll-behavior: none; /* 防止整页弹性滚动 */
  /* 极端情况下可用，但要小心使用 */
  /* position: fixed; 
     width: 100%;
     height: 100%; */
}

/* 防止iOS橡皮筋效果 */
.scroll-container {
  overscroll-behavior-y: contain;
  -webkit-overflow-scrolling: touch;
}
```

### 5. 容器查询适配

对于复杂布局，考虑使用容器查询进行更精细的控制：

```css
.scroll-container {
  container-type: inline-size;
  height: 100%;
}

@container (min-width: 400px) {
  .scroll-content {
    /* 容器宽度大于400px时的样式 */
    padding: 20px;
    columns: 2;
  }
}

@container (max-width: 399px) {
  .scroll-content {
    /* 小屏幕时的样式 */
    padding: 10px;
  }
}
```

## 适配场景验证

### 1. 浏览器环境
- ✅ 地址栏自动隐藏/显示
- ✅ 工具栏存在/隐藏
- ✅ 横竖屏切换

### 2. PWA 应用环境
- ✅ 全屏模式
- ✅ 安全区域适配
- ✅ 状态栏透明/半透明

### 3. 特殊情况
- ✅ 虚拟键盘弹出
- ✅ 通知栏下拉
- ✅ 分屏模式

## 多环境适配优势

该方案的核心优势在于可以同时适配多种环境：

1. **普通浏览器**：有地址栏、工具栏时正常工作
2. **PWA全屏模式**：无浏览器UI元素时也能正确计算高度  
3. **WebView容器**：在原生应用WebView中也能正确适配
4. **不同设备**：自动适配各种屏幕尺寸和安全区域

## 完整示例

```html
<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <style>
    :root {
      --navbar-height: 60px;
      --tabbar-height: 60px;
      --actual-vh: 100vh;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    #app {
      height: calc(
        var(--actual-vh)
        - var(--navbar-height)
        - var(--tabbar-height)
        - env(safe-area-inset-top)
        - env(safe-area-inset-bottom)
      );
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .navbar {
      height: var(--navbar-height);
      background: #007AFF;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: env(safe-area-inset-top);
    }

    .content {
      flex: 1;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      padding: 20px;
    }

    .tabbar {
      height: var(--tabbar-height);
      background: #f8f9fa;
      border-top: 1px solid #dee2e6;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding-bottom: env(safe-area-inset-bottom);
    }

    /* 兼容性回退 */
    @supports not (height: env(safe-area-inset-top)) {
      #app {
        height: calc(var(--actual-vh) - var(--navbar-height) - var(--tabbar-height));
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <nav class="navbar">导航栏</nav>
    <main class="content">
      <div style="height: 2000px; background: linear-gradient(to bottom, #ff7e5f, #feb47b);">
        长内容区域，可以滚动
      </div>
    </main>
    <div class="tabbar">
      <span>首页</span>
      <span>分类</span>
      <span>我的</span>
    </div>
  </div>

  <script>
    // 处理视口变化
    function handleViewportChange() {
      const vh = window.visualViewport?.height || window.innerHeight;
      document.documentElement.style.setProperty('--actual-vh', `${vh}px`);
    }

    handleViewportChange();
    window.visualViewport?.addEventListener('resize', handleViewportChange);
    window.addEventListener('resize', handleViewportChange);
  </script>
</body>
</html>
```

## 总结

这个内容区域适配方案的核心优势：

1. **统一性**：同一套代码适配浏览器和 PWA 环境
2. **精确性**：通过 calc() 精确计算可用高度
3. **动态性**：自动适配地址栏、键盘等动态变化
4. **兼容性**：提供完整的兼容性回退方案
5. **性能**：避免布局跳动，提供流畅体验

通过这种方式，可以确保内容区域在任何设备、任何浏览器环境下都能完美适配，解决移动端适配的核心难题。

## 重要注意事项

在实际应用中需要注意以下几点：

- **定期测试**：在不同设备和浏览器中测试兼容性
- **横屏适配**：考虑横屏模式下的布局调整
- **相对单位优先**：避免使用固定像素值，优先使用相对单位和CSS变量
- **极端尺寸处理**：使用媒体查询处理超大或超小屏幕设备
- **性能监控**：关注滚动性能和内存使用情况
- **渐进增强**：为不支持新特性的浏览器提供回退方案
