
<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-23 14:17:30
 * @LastEditors  : Bruce
 * @LastEditTime : 2025-06-24 14:21:01
 * @FilePath     : /.feng1/roles/ui_to_code.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-23 14:17:30
-->

# 设计图到代码实现的标准Prompt

## 任务目标

根据提供的UI设计图，精确实现前端页面开发，确保开发结果与设计图完全一致。

## 具体要求

请根据我提供的UI设计图，帮助我实现前端页面开发。请注意以下几点：

1. **保持代码结构**：除非特别说明，请保持现有HTML/组件结构，专注于样式实现
2. **使用SCSS**：请使用SCSS语法编写样式
3. **精确还原**：确保以下元素与设计图完全一致：
   - **【重点】布局定位与排列**：必须精确分析元素的定位方式、对齐关系和整体布局架构
   - **【重点】元素尺寸与间距**：精确测量并还原各元素尺寸、边距和内间距
   - **【重点】字体大小、字重与行高**：准确还原所有文本的视觉特征
   - **【重点】对齐方式与文本处理**：包括文本对齐、换行、截断和特殊处理方式
   - **【重点】颜色与渐变效果**：提取准确的色值和渐变参数，包括色值、角度和过渡点
   - 阴影效果与圆角：包括阴影扩散、模糊度、颜色和偏移
   - 透明度与叠加效果：包括透明度设置和层叠显示规则
   - 边框样式与粗细：详细还原各类边框的样式特性

## 工作流程

1. **仔细分析设计图**：请先详细观察我提供的设计图，理解设计意图和视觉效果
   - 识别所有关键视觉元素及其特性
   - 测量元素之间的尺寸和间距
   - 提取准确的颜色值和渐变参数
   - 分析阴影和特效的实现方式
2. **对比现有实现**：分析当前代码与设计图的差异
3. **提出SCSS方案**：详细列出需要调整的样式属性及其值，使用SCSS语法
4. **等待确认**：在实施任何代码更改前，等待我确认你的分析和方案
5. **实现样式代码**：提供完整的CSS代码
6. **验证与调整**：检查实现效果，必要时提供微调建议

## 响应格式

1. **设计图分析**：详细描述设计图中的关键元素、布局结构、颜色方案和视觉特点
   - **【重点分析】元素布局与排列方式**：详细分析元素之间的位置关系、定位方式和对齐特性
   - **【重点分析】具体尺寸、间距和比例关系**：提供准确的数值测量结果
   - **【重点分析】颜色值、渐变和阴影参数**：精确提取设计图中使用的颜色值和特效参数
   - **【重点分析】文本样式与处理方式**：分析字体、大小、间距、对齐方式等特性
2. **现有代码分析**：指出当前实现与设计图的主要差异
3. **SCSS调整方案**：详细列出需要修改的样式属性及建议值，使用SCSS语法组织
4. **确认请求**：请求用户确认分析和方案是否正确
5. **完整SCSS代码**：得到确认后，提供可以直接应用的SCSS代码
6. **实现说明**：解释关键样式决策和技术要点

请注意：如果设计图中有特殊效果（如复杂动画、特殊交互等）需要额外说明，我会在提供设计图时一并告知。

## 示例

"请根据我提供的UI设计图，帮助我实现前端页面。请使用SCSS语法编写样式代码，并先分析设计图中的视觉元素和布局，然后提出样式实现方案。我会确认你的分析后，你再提供具体的SCSS代码实现。"

[在这里我会附上设计图和相关代码]
