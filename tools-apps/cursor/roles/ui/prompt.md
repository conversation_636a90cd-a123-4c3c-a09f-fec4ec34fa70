
<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-06-24 17:36:25
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-06-24 20:21:25
 * @FilePath     : /Afeng/roles/ui/prompt.md
 * @Description  : UI设计分析与代码生成流程指导
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-06-24 17:36:25
-->

# UI设计分析与代码转换流程

## 前置要求

执行本流程前，必须提供以下资源：

- **UI设计图**：必须提供要分析的设计图文件或链接
- **目标代码文件路径**：需要指定生成代码的目标文件路径（如 `src/components/xxx/YourComponent.vue`）

> **重要提示**：如果没有提供上述任一资源，请不要执行后续步骤，而是向用户提示缺少的必要资源，并请求用户提供。例如：“请提供 UI 设计图和目标代码文件路径才能继续执行。”

**严格按照以下步骤执行**

## 步骤一：设计分析

使用 `Afeng/roles/ui/ui_design_analyzer.md`中的规则对目标设计图进行全面分析, 输出分析结果,

必须将分析结果输出到以下文件：

> **重要提示**：`Afeng/roles/ui/analyzer_out.md` 和 `Afeng/roles/ui/analyzer_out.json` 文件已经存在，直接写入内容即可，无需重新创建文件

- `Afeng/roles/ui/analyzer_out.md`：包含详细的设计分析说明、组件结构和样式指南
- `Afeng/roles/ui/analyzer_out.json`：包含结构化的组件配置数据，便于代码生成使用

## 步骤二：代码生成

 必须等待 `步骤一` 分析结果完成,

 然后，必须仔细阅读分析结果文件 `Afeng/roles/ui/analyzer_out.md` 和 `Afeng/roles/ui/analyzer_out.json`，充分理解其中的组件结构、样式和功能需求。然后必须按照 `Afeng/roles/ui/ui_to_code.md` 中的规则和指导，基于这些分析结果进行代码开发：

- 生成符合  `Afeng/roles/ui/analyzer_out.md`, `Afeng/roles/ui/analyzer_out.json` 要求的代码
