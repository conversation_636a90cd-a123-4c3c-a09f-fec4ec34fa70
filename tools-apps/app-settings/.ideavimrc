" .ideavimrc is a configuration file for IdeaVim plugin. It uses
"   the same commands as the original .vimrc configuration.
" You can find a list of commands here: https://jb.gg/h38q75
" Find more examples here: https://jb.gg/share-ideavimrc

" Source your .vimrc
source ~/.vimrc

"" -- Suggested options --
" Show a few lines of context around the cursor. Note that this makes the
" text scroll if you mouse-click near the start or end of the window.

" Do incremental searching.
set incsearch

" Don't use Ex mode, use Q for formatting.
map Q gq

" --- Enable IdeaVim plugins https://jb.gg/ideavim-plugins

" Highlight copied text
Plug 'machakann/vim-highlightedyank'
" Commentary plugin
Plug 'tpope/vim-commentary'
" EasyMotion plugin
Plug 'easymotion/vim-easymotion'

nmap f <Plug>(easymotion-bd-w)


" .ideavim 自定义配置
"设置在光标距离窗口顶部或底部一定行数时，开始滚动屏幕内容的行为
set scrolloff=3

set clipboard^=unamed,unnamedplus
set highlightedyank

" 正常模式下
noremap <C-j> 3j
noremap <C-k> 3k
noremap <C-h> 5h
noremap <C-l> 5l
" 可视模式下
vnoremap <C-j> 3j
vnoremap <C-k> 3k
vnoremap <C-h> 5h
vnoremap <C-l> 5l


nnoremap cw ciw
nnoremap c( ci(
nnoremap c) ci)
nnoremap c[ ci[
nnoremap c] ci]
nnoremap c{ ci{
nnoremap c} ci}
nnoremap c" ci"

nnoremap yw yiw
nnoremap y( yi(
nnoremap y) yi)
nnoremap y[ yi[
nnoremap y] yi]
nnoremap y{ yi{
nnoremap y} yi}
nnoremap y" yi"

nnoremap dw diw
nnoremap d( di(
nnoremap d) di)
nnoremap d[ di[
nnoremap d] di]
nnoremap d{ di{
nnoremap d} di}
nnoremap d" di"
" 可视模式下
xnoremap d "_d


" 针对 dd、diw 和其他 d 操作，不将删除的内容放入 Vim 和系统剪贴板
nnoremap dd "_dd
nnoremap diw "_diw
nnoremap d( "_di(
nnoremap d) "_di)
nnoremap d[ "_di[
nnoremap d] "_di]
nnoremap d{ "_di{
nnoremap d} "_di}
nnoremap d" "_di"


nnoremap \ %
nnoremap n *
nnoremap N #



"切换标签页
map <A-[> gT
map <A-]> gt
map - :execute "normal! \<C-O>"<CR>
map = :execute "normal! \<C-i>"<CR>

" 设置 EasyMotion 的跳转目标的背景色和文字颜色
highlight EasyMotionTarget guifg=#e9f01d guibg=#000000
set guifont=:h12

"查看源码有用，直接跳到源码定义的地方
map <silent> % :action GotoDeclaration<CR>








"" -- Map IDE actions to IdeaVim -- https://jb.gg/abva4t
"" Map \r to the Reformat Code action
"map \r <Action>(ReformatCode)

"" Map <leader>d to start debug
"map <leader>d <Action>(Debug)

"" Map \b to toggle the breakpoint on the current line
"map \b <Action>(ToggleLineBreakpoint)
