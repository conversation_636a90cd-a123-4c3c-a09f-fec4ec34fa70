<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Default Bookmark Guid</key>
	<string>3AD5BAFA-DF8F-43A3-89CF-E3B3DC16E7EC</string>
	<key>EnableProxyIcon</key>
	<false/>
	<key>FlashTabBarInFullscreen</key>
	<true/>
	<key>GlobalKeyMap</key>
	<dict>
		<key>0xd-0x20000-0x24</key>
		<dict>
			<key>Action</key>
			<integer>12</integer>
			<key>Keycode</key>
			<integer>13</integer>
			<key>Modifiers</key>
			<integer>131072</integer>
			<key>Text</key>
			<string>\n</string>
			<key>Version</key>
			<integer>1</integer>
		</dict>
		<key>0xf702-0x300000-0x7b</key>
		<dict>
			<key>Action</key>
			<integer>2</integer>
			<key>Apply Mode</key>
			<integer>0</integer>
			<key>Escaping</key>
			<integer>2</integer>
			<key>Text</key>
			<string></string>
			<key>Version</key>
			<integer>2</integer>
		</dict>
		<key>0xf703-0x300000-0x7c</key>
		<dict>
			<key>Action</key>
			<integer>0</integer>
			<key>Apply Mode</key>
			<integer>0</integer>
			<key>Escaping</key>
			<integer>2</integer>
			<key>Text</key>
			<string></string>
			<key>Version</key>
			<integer>2</integer>
		</dict>
	</dict>
	<key>HapticFeedbackForEsc</key>
	<false/>
	<key>HotkeyMigratedFromSingleToMulti</key>
	<true/>
	<key>MaxVertically</key>
	<false/>
	<key>New Bookmarks</key>
	<array>
		<dict>
			<key>ASCII Anti Aliased</key>
			<true/>
			<key>Ambiguous Double Width</key>
			<false/>
			<key>Ansi 0 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 1 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 10 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 11 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 12 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 13 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 14 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 15 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 2 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 3 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 4 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 5 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 6 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 7 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 8 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 9 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>BM Growl</key>
			<true/>
			<key>Background Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Background Image Location</key>
			<string></string>
			<key>Badge Color</key>
			<dict>
				<key>Alpha Component</key>
				<real>0.5</real>
				<key>Blue Component</key>
				<real>0.13960540294647217</real>
				<key>Color Space</key>
				<string>P3</string>
				<key>Green Component</key>
				<real>0.25479039549827576</real>
				<key>Red Component</key>
				<real>0.92929404973983765</real>
			</dict>
			<key>Blink Allowed</key>
			<true/>
			<key>Blinking Cursor</key>
			<false/>
			<key>Blur</key>
			<false/>
			<key>Bold Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Character Encoding</key>
			<integer>4</integer>
			<key>Close Sessions On End</key>
			<true/>
			<key>Columns</key>
			<integer>160</integer>
			<key>Command</key>
			<string></string>
			<key>Cursor Boost</key>
			<real>0.13</real>
			<key>Cursor Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Cursor Guide Color</key>
			<dict>
				<key>Alpha Component</key>
				<real>0.25</real>
				<key>Blue Component</key>
				<real>0.99125725030899048</real>
				<key>Color Space</key>
				<string>P3</string>
				<key>Green Component</key>
				<real>0.92047786712646484</real>
				<key>Red Component</key>
				<real>0.7486259937286377</real>
			</dict>
			<key>Cursor Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Cursor Type</key>
			<integer>1</integer>
			<key>Custom Command</key>
			<string>No</string>
			<key>Custom Directory</key>
			<string>Recycle</string>
			<key>Default Bookmark</key>
			<string>No</string>
			<key>Description</key>
			<string>Default</string>
			<key>Disable Window Resizing</key>
			<true/>
			<key>Flashing Bell</key>
			<false/>
			<key>Foreground Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Guid</key>
			<string>3AD5BAFA-DF8F-43A3-89CF-E3B3DC16E7EC</string>
			<key>Horizontal Spacing</key>
			<real>1</real>
			<key>Idle Code</key>
			<integer>0</integer>
			<key>Jobs to Ignore</key>
			<array>
				<string>rlogin</string>
				<string>ssh</string>
				<string>slogin</string>
				<string>telnet</string>
			</array>
			<key>Keyboard Map</key>
			<dict>
				<key>0x2d-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x32-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x00</string>
				</dict>
				<key>0x33-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b</string>
				</dict>
				<key>0x34-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1c</string>
				</dict>
				<key>0x35-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1d</string>
				</dict>
				<key>0x36-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1e</string>
				</dict>
				<key>0x37-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x38-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x7f</string>
				</dict>
				<key>0xf700-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2A</string>
				</dict>
				<key>0xf700-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5A</string>
				</dict>
				<key>0xf700-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6A</string>
				</dict>
				<key>0xf700-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x41</string>
				</dict>
				<key>0xf701-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2B</string>
				</dict>
				<key>0xf701-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5B</string>
				</dict>
				<key>0xf701-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6B</string>
				</dict>
				<key>0xf701-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x42</string>
				</dict>
				<key>0xf702-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2D</string>
				</dict>
				<key>0xf702-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5D</string>
				</dict>
				<key>0xf702-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6D</string>
				</dict>
				<key>0xf702-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x44</string>
				</dict>
				<key>0xf703-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2C</string>
				</dict>
				<key>0xf703-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5C</string>
				</dict>
				<key>0xf703-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6C</string>
				</dict>
				<key>0xf703-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x43</string>
				</dict>
				<key>0xf704-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2P</string>
				</dict>
				<key>0xf705-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2Q</string>
				</dict>
				<key>0xf706-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2R</string>
				</dict>
				<key>0xf707-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2S</string>
				</dict>
				<key>0xf708-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[15;2~</string>
				</dict>
				<key>0xf709-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[17;2~</string>
				</dict>
				<key>0xf70a-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[18;2~</string>
				</dict>
				<key>0xf70b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[19;2~</string>
				</dict>
				<key>0xf70c-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[20;2~</string>
				</dict>
				<key>0xf70d-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[21;2~</string>
				</dict>
				<key>0xf70e-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[23;2~</string>
				</dict>
				<key>0xf70f-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[24;2~</string>
				</dict>
				<key>0xf729-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2H</string>
				</dict>
				<key>0xf729-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5H</string>
				</dict>
				<key>0xf72b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2F</string>
				</dict>
				<key>0xf72b-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5F</string>
				</dict>
			</dict>
			<key>Link Color</key>
			<dict>
				<key>Alpha Component</key>
				<real>1</real>
				<key>Blue Component</key>
				<real>0.70932406187057495</real>
				<key>Color Space</key>
				<string>P3</string>
				<key>Green Component</key>
				<real>0.35333043336868286</real>
				<key>Red Component</key>
				<real>0.1451396644115448</real>
			</dict>
			<key>Match Background Color</key>
			<dict>
				<key>Alpha Component</key>
				<real>1</real>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Color Space</key>
				<string>P3</string>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Minimum Contrast</key>
			<real>0.080000000000000002</real>
			<key>Mouse Reporting</key>
			<true/>
			<key>Name</key>
			<string>Default</string>
			<key>Non Ascii Font</key>
			<string>Monaco 12</string>
			<key>Non-ASCII Anti Aliased</key>
			<true/>
			<key>Normal Font</key>
			<string>Menlo-Regular 17</string>
			<key>Option Key Sends</key>
			<integer>0</integer>
			<key>Prompt Before Closing 2</key>
			<false/>
			<key>Right Option Key Sends</key>
			<integer>0</integer>
			<key>Rows</key>
			<integer>50</integer>
			<key>Screen</key>
			<integer>-1</integer>
			<key>Scrollback Lines</key>
			<integer>1000</integer>
			<key>Selected Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Selection Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.8353000283241272</real>
				<key>Red Component</key>
				<real>0.70980000495910645</real>
			</dict>
			<key>Send Code When Idle</key>
			<false/>
			<key>Shortcut</key>
			<string></string>
			<key>Silence Bell</key>
			<false/>
			<key>Sync Title</key>
			<false/>
			<key>Tags</key>
			<array/>
			<key>Terminal Type</key>
			<string>xterm-256color</string>
			<key>Transparency</key>
			<real>0.0</real>
			<key>Unlimited Scrollback</key>
			<false/>
			<key>Use Bold Font</key>
			<true/>
			<key>Use Bright Bold</key>
			<true/>
			<key>Use Italic Font</key>
			<true/>
			<key>Use Non-ASCII Font</key>
			<false/>
			<key>Vertical Spacing</key>
			<real>1</real>
			<key>Visual Bell</key>
			<true/>
			<key>Window Type</key>
			<integer>0</integer>
			<key>Working Directory</key>
			<string>/Users/<USER>/string>
		</dict>
		<dict>
			<key>ASCII Anti Aliased</key>
			<true/>
			<key>Ambiguous Double Width</key>
			<false/>
			<key>Ansi 0 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 1 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 10 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 11 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 12 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 13 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 14 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 15 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Ansi 2 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 3 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 4 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 5 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 6 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Ansi 7 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Ansi 8 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>0.3333333432674408</real>
			</dict>
			<key>Ansi 9 Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.3333333432674408</real>
				<key>Green Component</key>
				<real>0.3333333432674408</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>BM Growl</key>
			<true/>
			<key>Background Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Background Image Location</key>
			<string></string>
			<key>Blink Allowed</key>
			<true/>
			<key>Blinking Cursor</key>
			<false/>
			<key>Blur</key>
			<false/>
			<key>Bold Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Bound Hosts</key>
			<array/>
			<key>Character Encoding</key>
			<integer>4</integer>
			<key>Close Sessions On End</key>
			<true/>
			<key>Columns</key>
			<integer>160</integer>
			<key>Command</key>
			<string></string>
			<key>Cursor Boost</key>
			<real>0.13</real>
			<key>Cursor Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Cursor Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>1</real>
				<key>Red Component</key>
				<real>1</real>
			</dict>
			<key>Cursor Type</key>
			<integer>1</integer>
			<key>Custom Command</key>
			<string>No</string>
			<key>Custom Directory</key>
			<string>Recycle</string>
			<key>Default Bookmark</key>
			<string>No</string>
			<key>Description</key>
			<string>Default</string>
			<key>Disable Window Resizing</key>
			<true/>
			<key>Flashing Bell</key>
			<false/>
			<key>Foreground Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.73333334922790527</real>
				<key>Green Component</key>
				<real>0.73333334922790527</real>
				<key>Red Component</key>
				<real>0.73333334922790527</real>
			</dict>
			<key>Guid</key>
			<string>8BF57F33-7CCF-4C92-8958-B627FBB79B6E</string>
			<key>Horizontal Spacing</key>
			<real>1</real>
			<key>Idle Code</key>
			<integer>0</integer>
			<key>Jobs to Ignore</key>
			<array>
				<string>rlogin</string>
				<string>ssh</string>
				<string>slogin</string>
				<string>telnet</string>
			</array>
			<key>Keyboard Map</key>
			<dict>
				<key>0x2d-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x32-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x00</string>
				</dict>
				<key>0x33-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b</string>
				</dict>
				<key>0x34-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1c</string>
				</dict>
				<key>0x35-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1d</string>
				</dict>
				<key>0x36-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1e</string>
				</dict>
				<key>0x37-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1f</string>
				</dict>
				<key>0x38-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x7f</string>
				</dict>
				<key>0xf700-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2A</string>
				</dict>
				<key>0xf700-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5A</string>
				</dict>
				<key>0xf700-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6A</string>
				</dict>
				<key>0xf700-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x41</string>
				</dict>
				<key>0xf701-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2B</string>
				</dict>
				<key>0xf701-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5B</string>
				</dict>
				<key>0xf701-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6B</string>
				</dict>
				<key>0xf701-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x42</string>
				</dict>
				<key>0xf702-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2D</string>
				</dict>
				<key>0xf702-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5D</string>
				</dict>
				<key>0xf702-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6D</string>
				</dict>
				<key>0xf702-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x44</string>
				</dict>
				<key>0xf703-0x220000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2C</string>
				</dict>
				<key>0xf703-0x240000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5C</string>
				</dict>
				<key>0xf703-0x260000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;6C</string>
				</dict>
				<key>0xf703-0x280000</key>
				<dict>
					<key>Action</key>
					<integer>11</integer>
					<key>Text</key>
					<string>0x1b 0x1b 0x5b 0x43</string>
				</dict>
				<key>0xf704-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2P</string>
				</dict>
				<key>0xf705-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2Q</string>
				</dict>
				<key>0xf706-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2R</string>
				</dict>
				<key>0xf707-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2S</string>
				</dict>
				<key>0xf708-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[15;2~</string>
				</dict>
				<key>0xf709-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[17;2~</string>
				</dict>
				<key>0xf70a-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[18;2~</string>
				</dict>
				<key>0xf70b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[19;2~</string>
				</dict>
				<key>0xf70c-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[20;2~</string>
				</dict>
				<key>0xf70d-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[21;2~</string>
				</dict>
				<key>0xf70e-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[23;2~</string>
				</dict>
				<key>0xf70f-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[24;2~</string>
				</dict>
				<key>0xf729-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2H</string>
				</dict>
				<key>0xf729-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5H</string>
				</dict>
				<key>0xf72b-0x20000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;2F</string>
				</dict>
				<key>0xf72b-0x40000</key>
				<dict>
					<key>Action</key>
					<integer>10</integer>
					<key>Text</key>
					<string>[1;5F</string>
				</dict>
			</dict>
			<key>Minimum Contrast</key>
			<real>0.080000000000000002</real>
			<key>Mouse Reporting</key>
			<true/>
			<key>Name</key>
			<string>New Profile</string>
			<key>Non Ascii Font</key>
			<string>Monaco 12</string>
			<key>Non-ASCII Anti Aliased</key>
			<true/>
			<key>Normal Font</key>
			<string>Menlo-Regular 17</string>
			<key>Option Key Sends</key>
			<integer>0</integer>
			<key>Prompt Before Closing 2</key>
			<false/>
			<key>Right Option Key Sends</key>
			<integer>0</integer>
			<key>Rows</key>
			<integer>50</integer>
			<key>Screen</key>
			<integer>-1</integer>
			<key>Scrollback Lines</key>
			<integer>1000</integer>
			<key>Selected Text Color</key>
			<dict>
				<key>Blue Component</key>
				<real>0.0</real>
				<key>Green Component</key>
				<real>0.0</real>
				<key>Red Component</key>
				<real>0.0</real>
			</dict>
			<key>Selection Color</key>
			<dict>
				<key>Blue Component</key>
				<real>1</real>
				<key>Green Component</key>
				<real>0.8353000283241272</real>
				<key>Red Component</key>
				<real>0.70980000495910645</real>
			</dict>
			<key>Send Code When Idle</key>
			<false/>
			<key>Shortcut</key>
			<string></string>
			<key>Silence Bell</key>
			<false/>
			<key>Sync Title</key>
			<false/>
			<key>Tags</key>
			<array/>
			<key>Terminal Type</key>
			<string>xterm-256color</string>
			<key>Transparency</key>
			<real>0.0</real>
			<key>Unlimited Scrollback</key>
			<false/>
			<key>Use Bold Font</key>
			<true/>
			<key>Use Bright Bold</key>
			<true/>
			<key>Use Italic Font</key>
			<true/>
			<key>Use Non-ASCII Font</key>
			<false/>
			<key>Vertical Spacing</key>
			<real>1</real>
			<key>Visual Bell</key>
			<true/>
			<key>Window Type</key>
			<integer>0</integer>
			<key>Working Directory</key>
			<string>/Users/<USER>/string>
		</dict>
	</array>
	<key>OpenArrangementAtStartup</key>
	<false/>
	<key>OpenNoWindowsAtStartup</key>
	<false/>
	<key>PointerActions</key>
	<dict>
		<key>Button,1,1,,</key>
		<dict>
			<key>Action</key>
			<string>kContextMenuPointerAction</string>
		</dict>
		<key>Button,2,1,,</key>
		<dict>
			<key>Action</key>
			<string>kPasteFromClipboardPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeDown,,</key>
		<dict>
			<key>Action</key>
			<string>kPrevWindowPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeLeft,,</key>
		<dict>
			<key>Action</key>
			<string>kPrevTabPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeRight,,</key>
		<dict>
			<key>Action</key>
			<string>kNextTabPointerAction</string>
		</dict>
		<key>Gesture,ThreeFingerSwipeUp,,</key>
		<dict>
			<key>Action</key>
			<string>kNextWindowPointerAction</string>
		</dict>
	</dict>
	<key>Print In Black And White</key>
	<true/>
	<key>SoundForEsc</key>
	<false/>
	<key>StretchTabsToFillBar</key>
	<false/>
	<key>VisualIndicatorForEsc</key>
	<false/>
	<key>findMode_iTerm</key>
	<integer>0</integer>
</dict>
</plist>
