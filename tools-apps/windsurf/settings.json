{
    "windsurf.autocompleteSpeed": "fast",
    "editor.fontSize": 14,
    "window.zoomLevel": 1,
		"editor.scrollBeyondLastLine": false,
		// 下面的设置主要是为了突出显示当前标签页
		"workbench.editor.highlightModifiedTabs": true, // 突出显示已修改的标签
		"workbench.editor.activeTabBold": true, // 当前标签页字体加粗
		"workbench.editor.showTabs": "multiple", // 显示多个标签
		"workbench.editor.decorations.badges": true, // 显示文件装饰器
		"workbench.editor.decorations.colors": true, // 显示颜色装饰器
		"workbench.colorCustomizations": {
			"tab.activeBackground": "#374456", // 当前标签背景色
			"tab.activeBorder": "#60a5fa", // 当前标签边框色
			"tab.activeForeground": "#ffffff" // 当前标签文字颜色
		}
}


{
	"workbench.editor.enablePreview": false,
    "workbench.activityBar.orientation": "vertical",
    "window.newWindowDimensions": "offset",
    "editor.tabSize": 2,
    "editor.detectIndentation": false,
    "[python]": {
      "editor.insertSpaces": true,
      "editor.tabSize": 4,
      "editor.renderWhitespace": "all"
    },
    "editor.renderWhitespace": "none",
    "editor.minimap.enabled": false,
    "editor.wordWrap": "off",
    "window.zoomLevel": 1,
    "editor.fontSize": 14,
    "editor.insertSpaces": false,
    "editor.formatOnPaste": true,
    "editor.formatOnType": true,
    "editor.scrollBeyondLastLine": false,
    "editor.find.addExtraSpaceOnTop": true,
    "editor.find.seedSearchStringFromSelection": false,
    "editor.selectionHighlight": false,
    "editor.trimAutoWhitespace": true,
    "editor.suggestSelection": "first",
    "editor.suggest.localityBonus": true,
    "editor.largeFileOptimizations": true,
    "editor.hover.enabled": true,
    "editor.hover.sticky": true,
    "editor.mouseWheelZoom": false,
    "editor.codeActionsOnSave": {

      "source.fixAll.markdownlint": "explicit"
    },
    "eslint.options": {
      "plugins": ["html"]
    },
    "guides.active.width": 1,
    "guides.active.color.dark": "yellow",
    "[markdown]": {
      "editor.quickSuggestions": {
        "comments": "on",
        "strings": "on",
        "other": "on"
      }
    },
    "search.exclude": {
      "**/node_modules": true,
      "library/": true
    },
    "python.pythonPath": "/usr/local/bin/python3",
    "emmet.syntaxProfiles": {
      "vue-html": "html",
      "vue": "html"
    },
    "emmet.includeLanguages": {
      "wxml": "html"
    },
    "emmet.preferences": {
      "css.webkitProperties": "border-right,animation",
      "css.mozProperties": "",
      "css.oProperties": null,
      "css.msProperties": null
    },
    "emmet.showSuggestionsAsSnippets": true,
    "emmet.showExpandedAbbreviation": "inMarkupAndStylesheetFilesOnly",
    "editor.snippetSuggestions": "top",
    "files.encoding": "utf8",
    "files.autoSave": "off",
    "files.trimTrailingWhitespace": true,
    "files.associations": {
      "*.wpy": "vue",
      "*.cjson": "jsonc",
      "*.wxss": "css",
      "*.wxs": "javascript",
      "*.tmpl": "html",
      "*.json": "jsonc"
    },
    "files.eol": "\n",
    "files.exclude": {
      "**/.git": false,
      "**/.svn": true,
      "**/.hg": true,
      "**/CVS": true,
      ".idea": true,
      ".vscode": false,
      "**/.DS_Store": true,
      "**/bower_components": true,
      "**/jspm_packages": true,
      "**/node_modules": false
    },
    "workbench.iconTheme": "vscode-great-icons",
    "workbench.startupEditor": "newUntitledFile",
      "workbench.sideBar.location": "left",
    "workbench.colorCustomizations": {
      "gitlens.trailingLineForegroundColor": "#00ffa6af",
      "gitlens.trailingLineBackgroundColor": "#838382dd",
      "bookmarks.lineBorder": "#007dff",
      "editorCursor.foreground": "#ffd900c2",
      "editor.selectionBackground": "#2d2dc4",
      "editorLineNumber.activeForeground": "#12c678",
      "editor.lineHighlightBackground": "#1073cf2d",
      "editor.findMatchBackground": "#2db3ff",
      "peekViewResult.selectionBackground": "#3b92c469",
      "peekViewResult.background": "#2c4253",
      "peekViewEditor.matchHighlightBackground": "#ffd9007e",
      "peekViewEditorGutter.background": "#544545bf",
      "titleBar.activeForeground": "#ffd900c2",
      "tab.activeBorder": "#ffd900c2",
      "tab.activeBorderTop": "#ffd900c2",
      "tab.activeBackground": "#3b92c469",
      "list.activeSelectionForeground": "#fff",
      "list.activeSelectionBackground": "#1ad574d4",
      "list.inactiveSelectionBackground": "#1ad574d4",
      "list.inactiveSelectionForeground": "#fff",
      "list.hoverBackground": "#3b92c469",
      "editor.lineHighlightBorder": "#9fced11f"
    },
    "editor.tokenColorCustomizations": {},
    "editor.renderLineHighlight": "all",
    "explorer.confirmDragAndDrop": false,
    "explorer.confirmDelete": true,
    "explorer.autoReveal": true,
    "javascript.suggestionActions.enabled": false,
    "html.format.wrapAttributes": "auto",
    "highlightLine.borderStyle": "dashed",
    "highlightLine.borderWidth": "1px",
    "highlightLine.borderColor": "#FFD700",
    "highlight-matching-tag.styles": {
      "closing": {
        "full": {
          "surround": "#FFD700"
        }
      },
      "opening": {
        "full": {
          "surround": "#FFD700"
        }
      }
    },
    "sync.gist": "5993d31b8fc2bf2daad87387b9bf3a57",
    "sync.autoUpload": false,
    "sync.autoDownload": false,
    "terminal.integrated.windowsEnableConpty": true,
    "terminal.integrated.shell.osx": "/bin/zsh",
    "explorer.enableDragAndDrop": false,
    "explorer.incrementalNaming": "smart",
    "[javascript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "vim.sneak": true,
    "vim.surround": true,
    "vim.camelCaseMotion.enable": true,
    "vim.easymotion": true,
    "vim.easymotionKeys": "asdfghjklwenmio",
    "vim.easymotionMarkerBackgroundColor": "#000",
    "vim.easymotionMarkerForegroundColorOneChar": "#e6eba0",
    "vim.easymotionMarkerForegroundColorTwoCharFirst": "#E9F01D",
    "vim.easymotionMarkerForegroundColorTwoCharSecond": "#aeb108",
    "vim.easymotionMarkerHeight": 22,
    "vim.easymotionMarkerFontWeight": "bold",
    "vim.easymotionMarkerYOffset": 10,
    "vim.easymotionMarkerWidthPerChar": 15,
    "vim.easymotionMarkerFontFamily": "unset",
    "vim.highlightedyank.enable": true,
    "vim.highlightedyank.duration": 1000,
    "vim.useSystemClipboard": true,
    "vim.searchHighlightColor": "#2db3ff",
    "vim.searchHighlightTextColor": "black",
    "vim.leader": "<space>",
    "vim.handleKeys": {
      "<C-j>": true
    },
    "vim.insertModeKeyBindings": [
      {
        "before": ["j", "j"],
        "after": ["<Esc>"]
      },
      {
        "before": ["<C-j>"],
        "commands": ["cursorDown", "cursorDown", "cursorDown"]
      },
      {
        "before": ["<C-k>"],
        "commands": ["cursorUp", "cursorUp", "cursorUp"]
      },
      {
        "before": ["<C-l>"],
        "commands": [
          "cursorRight",
          "cursorRight",
          "cursorRight",
          "cursorRight",
          "cursorRight"
        ]
      },
      {
        "before": ["<C-h>"],
        "commands": [
          "cursorLeft",
          "cursorLeft",
          "cursorLeft",
          "cursorLeft",
          "cursorLeft"
        ]
      }
    ],
    "vim.visualModeKeyBindingsNonRecursive": [
      {
        "before": ["d"],
        "after": ["\"", "_", "d"]
      },
      {
        "before": ["D"],
        "after": ["\"", "_", "D"]
      },
      {
        "before": ["s"],
        "after": ["\"", "_", "s"]
      },
      {
        "before": ["<C-j>"],
        "after": ["3", "j"]
      },
      {
        "before": ["<C-k>"],
        "after": ["3", "k"]
      },
      {
        "before": ["<C-l>"],
        "after": ["5", "l"]
      },
      {
        "before": ["<C-h>"],
        "after": ["5", "h"]
      }
    ],
    "vim.normalModeKeyBindingsNonRecursive": [
      {
        "before": ["<leader>", "v", "{"],
        "after": ["0", "<Shift>", "v", "/", "{", "<CR>", "%"]
      },
      {
        "before": ["\\"],
        "commands": ["editor.action.jumpToBracket"]
      },
      {
        "before": ["g", "r"],
        "commands": ["editor.action.referenceSearch.trigger"]
      },
      {
        "before": ["[", "["],
        "after": ["[", "{"]
      },
      {
        "before": ["]", "]"],
        "after": ["]", "}"]
      },
      {
        "before": ["m", "m"],
        "after": ["z", "z"]
      },
      {
        "before": ["t", "t"],
        "after": ["z", "t"]
      },
      {
        "before": ["<C-j>"],
        "after": ["3", "j"]
      },
      {
        "before": ["<C-k>"],
        "after": ["3", "k"]
      },
      {
        "before": ["<C-l>"],
        "after": ["5", "l"]
      },
      {
        "before": ["<C-h>"],
        "after": ["5", "h"]
      },
      {
        "before": ["K"],
        "after": ["g", "t"]
      },
      {
        "before": ["J"],
        "after": ["g", "T"]
      },
      {
        "before": ["-"],
        "commands": ["workbench.action.navigateBack"]
      },
      {
        "before": ["="],
        "commands": ["workbench.action.navigateForward"]
      },
      {
        "before": ["X"],
        "commands": ["workbench.action.reopenClosedEditor"]
      },
      {
        "before": ["x"],
        "commands": ["workbench.action.closeActiveEditor"]
      },
      {
        "before": [","],
        "commands": ["highlight-matching-tag.jumpToMatchingTag"]
      },
      {
        "before": ["n"],
        "after": ["*"]
      },
      {
        "before": ["N"],
        "after": ["#"]
      },
      {
        "before": ["d"],
        "after": ["\"", "_", "d"]
      },
      {
        "before": ["D"],
        "after": ["\"", "_", "D"]
      },
      {
        "before": ["s"],
        "after": ["\"", "_", "s"]
      },
      {
        "before": ["d", "w"],
        "after": ["\"", "_", "d", "i", "w"]
      },
      {
        "before": ["d", "["],
        "after": ["\"", "_", "d", "i", "["]
      },
      {
        "before": ["d", "{"],
        "after": ["\"", "_", "d", "i", "{"]
      },
      {
        "before": ["d", "("],
        "after": ["\"", "_", "d", "i", "("]
      },
      {
        "before": ["d", "\""],
        "after": ["\"", "_", "d", "i", "\""]
      },
      {
        "before": ["d", "'"],
        "after": ["\"", "_", "d", "i", "'"]
      },
      {
        "before": ["d", "t"],
        "after": ["\"", "_", "d", "i", "t"]
      },
      {
        "before": ["y", "w"],
        "after": ["y", "i", "w"]
      },
      {
        "before": ["y", "["],
        "after": ["y", "i", "["]
      },
      {
        "before": ["y", "{"],
        "after": ["y", "i", "{"]
      },
      {
        "before": ["y", "("],
        "after": ["y", "i", "("]
      },
      {
        "before": ["y", "\""],
        "after": ["y", "i", "\""]
      },
      {
        "before": ["y", "'"],
        "after": ["y", "i", "'"]
      },
      {
        "before": ["y", "t"],
        "after": ["y", "i", "t"]
      },
      {
        "before": ["c", "w"],
        "after": ["c", "i", "w"]
      },
      {
        "before": ["c", "["],
        "after": ["c", "i", "["]
      },
      {
        "before": ["c", "{"],
        "after": ["c", "i", "{"]
      },
      {
        "before": ["c", "("],
        "after": ["c", "i", "("]
      },
      {
        "before": ["c", "\""],
        "after": ["c", "i", "\""]
      },
      {
        "before": ["c", "'"],
        "after": ["c", "i", "'"]
      },
      {
        "before": ["c", "t"],
        "after": ["c", "i", "t"]
      },
      {
        "before": ["v", "w"],
        "after": ["v", "i", "w"]
      },
      {
        "before": ["v", "["],
        "after": ["v", "i", "["]
      },
      {
        "before": ["v", "{"],
        "after": ["v", "i", "{"]
      },
      {
        "before": ["v", "("],
        "after": ["v", "i", "("]
      },
      {
        "before": ["v", "\""],
        "after": ["v", "i", "\""]
      },
      {
        "before": ["v", "'"],
        "after": ["v", "i", "'"]
      },
      {
        "before": ["v", "t"],
        "after": ["v", "i", "t"]
      },
      {
        "before": ["<leader>", "o"],
        "commands": ["breadcrumbs.focusAndSelect"]
      },
      {
        "before": ["W"],
        "after": ["<leader>", "w"]
      },
      {
        "before": ["B"],
        "after": ["<leader>", "b"]
      },
      {
        "before": ["f"],
        "after": ["<leader>", "<leader>", "<leader>", "b", "d", "w"]
      },
      {
        "before": ["<leader>", "j"],
        "after": ["<leader>", "<leader>", "<leader>", "b", "d", "j", "k"]
      }
    ],
    "javascript.updateImportsOnFileMove.enabled": "always",
    "vetur.format.defaultFormatter.html": "js-beautify-html",
    "gotoNextPreviousMember.symbolKinds": [
      "function",
      "method",
      "constructor",
      "object",
      "class",
      "field",
      "event"
    ],
    "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue",
    "debug.allowBreakpointsEverywhere": true,
    "commentTranslate.targetLanguage": "zh-CN",
    "liveServer.settings.donotShowInfoMsg": true,
    "[vue]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "vue-helper.alias": {
      "@": "."
    },
    "vue-helper.rem-px": 75,
    "minapp-vscode.disableAutoConfig": true,
    "[less]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
      "editor.defaultFormatter": "HookyQR.beautify"
    },
    "less.compile": {
      "out": true,
      "outExt": ".css"
    },
    "[javascriptreact]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "todo-tree.tree.showScanModeButton": false,
    "bookmarks.navigateThroughAllFiles": false,
    "leetcode.endpoint": "leetcode-cn",
    "leetcode.workspaceFolder": "/Users/<USER>/Desktop/self",
    "editor.cursorStyle": "line",
    "editor.lineNumbers": "on",
    "editor.wordSeparators": "/\\()\"':,.;<>~!@#$%^&*|+=[]{}`?-",
    "security.workspace.trust.untrustedFiles": "open",
    "editor.cursorSmoothCaretAnimation": "on",
    "selectby.regexes": {
      "goToExportDefault": {
        "moveby": "export default"
      },
      "goToStyle": {
        "moveby": "<style"
      },
      "goToMethods": {
        "moveby": "methods:"
      },
      "goToRender": {
        "moveby": "render()"
      }
    },
    "gitlens.currentLine.enabled": true,
    "gitlens.blame.heatmap.location": "right",
    "gitlens.blame.highlight.enabled": true,
    "gitlens.currentLine.format": "${author}, ${date}${' via 'pullRequest}${ • message|50?}",
    "gitlens.currentLine.dateFormat": "MM-DD",
    "gitlens.hovers.annotations.enabled": false,
    "gitlens.defaultDateFormat": "YYYY-MM-DD HH:mm",
    "gitlens.hovers.avatars": false,
    "gitlens.hovers.currentLine.details": true,
    "gitlens.hovers.detailsMarkdownFormat": "${avatar} &nbsp;__${author}__, ${ago}${' via 'pullRequest} &nbsp; _(${date})_ \n\n${message}\n\n${commands}${\n\n---\n\nfootnotes}",
    "[bat]": {},
    "workbench.editor.scrollToSwitchTabs": true,
    "editor.guides.indentation": false,
    "editor.inlineSuggest.enabled": true,
    "editor.accessibilitySupport": "off",
    "tabnine.experimentalAutoImports": true,
    "Notes.notesLocation": "/Users/<USER>/Desktop/前端笔记",
    "blockman.n01LineHeight": 0,
    "diffEditor.wordWrap": "off",
    "editor.guides.bracketPairs": false,
    "editor.inlayHints.enabled": false,
    "git.ignoreMissingGitWarning": true,
    "CodeGPT.query.language": "Chinese",
    "codeium.enableSearch": true,
    "codeium.enableConfig": {
      "*": false,
      "ini": true,
      "markdown": true
    },
    "easycode.userEmail": "<EMAIL>",
      "liveServer.settings.ChromeDebuggingAttachment": false,
    "easycode.model": "gpt-3.5-turbo",
    "explorer.autoRevealExclude": {
      "**/node_modules": false
    },
    "Codegeex.Privacy": true,
    "Codegeex.Explanation.LanguagePreference": "中文",
    "Codegeex.Comment.LanguagePreference": "中文",
    "Codegeex.DisabledFor": {
      "javascript": false
    },
    "aws.suppressPrompts": {
      "codeWhispererNewWelcomeMessage": true
    },
    "Codegeex.EnableExtension": false,
    "Codegeex.SidebarUI.LanguagePreference": "中文",
    "editor.inlineSuggest.suppressSuggestions": true,
    "extensions.autoUpdate": false,
    "CodeGPT.apiKey": "CodeGPT Plus",
    "CodeGPT.model": "CodeGPT Plus",
    "github.copilot.chat.localeOverride": "zh-CN",
    "editor.inlineSuggest.showToolbar": "always",
    "path-intellisense.extensionOnImport": true,
    "fileheader.configObj": {
      "createFileTime": true,
      "language": {
        "languagetest": {
          "head": "/$$",
          "middle": " $ @",
          "end": " $/"
        }
      },
      "autoAdd": true,
      "autoAddLine": 100,
      "autoAlready": true,
      "annotationStr": {
        "head": "/*",
        "middle": " * @",
        "end": " */",
        "use": false
      },
      "headInsertLine": {
        "php": 2,
        "sh": 2
      },
      "beforeAnnotation": {
        "文件后缀": "该文件后缀的头部注释之前添加某些内容"
      },
      "afterAnnotation": {
        "文件后缀": "该文件后缀的头部注释之后添加某些内容"
      },
      "specialOptions": {
        "特殊字段": "自定义比如LastEditTime/LastEditors"
      },
      "switch": {
        "newlineAddAnnotation": true
      },
      "supportAutoLanguage": [],
      "prohibitAutoAdd": ["json"],
      "folderBlacklist": ["node_modules", "文件夹禁止自动添加头部注释"],
      "prohibitItemAutoAdd": [
        "项目的全称, 整个项目禁止自动添加头部注释, 可以使用快捷键添加"
      ],
      "moveCursor": true,
      "dateFormat": "YYYY-MM-DD HH:mm:ss",
      "atSymbol": ["@", "@"],
      "atSymbolObj": {
        "文件后缀": ["头部注释@符号", "函数注释@符号"]
      },
      "colon": [": ", ": "],
      "colonObj": {
        "文件后缀": ["头部注释冒号", "函数注释冒号"]
      },
      "filePathColon": "路径分隔符替换",
      "showErrorMessage": false,
      "writeLog": false,
      "wideSame": true,
      "wideNum": 13,
      "functionWideNum": 0,
      "CheckFileChange": false,
      "createHeader": true,
      "useWorker": false,
      "designAddHead": false,
      "headDesignName": "random",
      "headDesign": false,
      "cursorModeInternalAll": {},
      "openFunctionParamsCheck": true,
      "functionParamsShape": ["{", "}"],
      "functionBlankSpaceAll": {},
      "functionTypeSymbol": "*",
      "typeParamOrder": "type param",
      "customHasHeadEnd": {},
      "throttleTime": 60000
    },
    "fileheader.cursorMode": {
      "description": "",
      "param": "Do not edit",
      "return": "Do not edit"
    },
    "fileheader.customMade": {
      "Author": "Bruce",
      "Date": "Do not edit",
      "LastEditors": "Bruce",
      "LastEditTime": "Do not edit",
      "FilePath": "no item name",
      "Description": "",
      "custom_string_obkoro1_copyright": "Copyright ${now_year} Bruce, All Rights Reserved. ",
      "custom_string_obkoro1_date": "Do not edit"
    },
    "github.copilot.enable": {
      "*": false,
      "plaintext": false,
      "markdown": false,
      "scminput": false,
      "vue": false,
      "javascript": false,
      "html": false
    },
    "extensions.ignoreRecommendations": true,
    "Lingma.LocalStoragePath": "/Users/<USER>/.lingma",
    "liveServer.settings.donotVerifyTags": true,
    "marscode.chatLanguage": "cn",
    "marscode.codeCompletionPro": {
      "enableCodeCompletionPro": true
    },
    "Lingma.DisplayLanguage": "简体中文",
    "fittencode.languagePreference.displayPreference": "zh-cn",
    "fittencode.languagePreference.commentPreference": "zh-cn",
    "Snap.codeGeneration.doNotDisturbMode": true,
    "cursor.cpp.disabledLanguages": [
      "plaintext",
      "markdown",
      "scminput",
      "vue",
      "javascript",
      "html"
    ],
      "kotlin.languageServer.enabled": false,
      "kotlin.debugAdapter.enabled": false,
			"diffEditor.ignoreTrimWhitespace": false,
			"windsurf.cascadeCommandsAllowList": [
				"cat",
				"kill",
				"lsof"
			],
			"windsurf.cascadeCommandsDenyList": [
				"rm"
			],
			"windsurf.autocompleteSpeed": "fast",
			"windsurf.autoExecutionPolicy": "off",
			"windsurf.explainAndFixInCurrentConversation": true,
			"remote.autoForwardPortsSource": "hybrid",
			"windsurf.chatFontSize": "default",
			"windsurf.rememberLastModelSelection": true,
			"windsurf.openRecentConversation": true
  }
